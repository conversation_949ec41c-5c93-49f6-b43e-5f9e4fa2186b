# Decimal 类型错误修复说明

## 问题描述

在运行系统时遇到以下错误：
```
AttributeError: Decimal
```

这是因为在 SQLAlchemy 模型中使用了 `db.Decimal`，但实际上应该从 `sqlalchemy` 模块导入 `Numeric` 类型。

## 修复内容

### 1. 修复的文件列表

以下文件已经修复了 Decimal 类型引用：

- `app/models/company.py`
- `app/models/goods.py`
- `app/models/warehouse.py`
- `app/models/vehicle.py`
- `app/models/driver.py`
- `app/models/order.py`
- `app/models/delivery.py`
- `app/models/finance.py`

### 2. 修复方法

**修复前：**
```python
from app import db
from .base import BaseModel

class Company(BaseModel):
    credit_limit = db.Column(db.Decimal(15, 2), default=0)
```

**修复后：**
```python
from app import db
from .base import BaseModel
from sqlalchemy import Numeric

class Company(BaseModel):
    credit_limit = db.Column(Numeric(15, 2), default=0)
```

### 3. 主要变更

1. **添加导入**：在每个使用 Decimal 的模型文件中添加 `from sqlalchemy import Numeric`
2. **替换类型**：将所有 `db.Decimal(x, y)` 替换为 `Numeric(x, y)`

## 测试验证

### 运行测试脚本

```bash
cd backend
python test_models.py
```

这个脚本会测试：
- 所有模型的导入
- 应用创建
- 数据库表创建
- 基本的CRUD操作

### 预期输出

```
==================================================
物流园区信息系统 - 模型测试
==================================================
测试模型导入...
✓ Flask应用导入成功
✓ User模型导入成功
✓ Company模型导入成功
✓ Warehouse模型导入成功
✓ Goods和Inventory模型导入成功
✓ Vehicle模型导入成功
✓ Driver模型导入成功
✓ Order和OrderItem模型导入成功
✓ Delivery模型导入成功
✓ Bill和Payment模型导入成功

所有模型导入成功！

测试应用创建...
✓ 应用创建成功
✓ 数据库上下文正常

测试模型创建...
✓ 数据库表创建成功
✓ 用户创建成功
✓ 用户查询成功

==================================================
✓ 所有测试通过！系统可以正常启动。

接下来可以运行:
python run.py
==================================================
```

## 启动系统

修复完成后，可以正常启动系统：

### 方法一：直接启动
```bash
cd backend
python run.py
```

### 方法二：使用 Flask 命令
```bash
cd backend
flask run
```

### 方法三：Docker 启动（推荐）
```bash
cd logistics_park_system
docker-compose up -d
```

## 数据库初始化

如果使用 SQLite 进行开发测试：

```bash
cd backend
python -c "
from app import create_app, db
app = create_app()
with app.app_context():
    db.create_all()
    print('数据库初始化完成')
"
```

## 注意事项

1. **数据类型兼容性**：`Numeric` 类型在不同数据库中都有良好的兼容性
2. **精度设置**：`Numeric(15, 2)` 表示总共15位数字，其中2位小数
3. **默认值**：所有金额字段都设置了默认值0，避免NULL值问题

## 相关文件

- `backend/test_models.py` - 模型测试脚本
- `backend/config/config_sqlite.py` - SQLite 配置文件
- `docs/WINDOWS_SETUP.md` - Windows 环境设置指南

## 后续建议

1. 在生产环境中使用 PostgreSQL 数据库
2. 定期运行测试脚本验证模型完整性
3. 使用数据库迁移工具管理表结构变更
