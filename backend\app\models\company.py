from app import db
from .base import BaseModel
from sqlalchemy import Numeric

class Company(BaseModel):
    """企业模型"""
    __tablename__ = 'companies'
    
    # 基本信息
    name = db.Column(db.String(100), nullable=False, index=True)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    type = db.Column(db.String(20), nullable=False)  # logistics, warehouse, transport
    
    # 联系信息
    contact_person = db.Column(db.String(50))
    contact_phone = db.Column(db.String(20))
    contact_email = db.Column(db.String(120))
    address = db.Column(db.Text)
    
    # 企业资质
    business_license = db.Column(db.String(50))  # 营业执照号
    tax_number = db.Column(db.String(50))  # 税号
    legal_person = db.Column(db.String(50))  # 法人代表
    
    # 状态信息
    status = db.Column(db.String(20), default='active')  # active, inactive, suspended
    entry_date = db.Column(db.Date)  # 入驻日期
    contract_start = db.Column(db.Date)  # 合同开始日期
    contract_end = db.Column(db.Date)  # 合同结束日期
    
    # 财务信息
    credit_limit = db.Column(Numeric(15, 2), default=0)  # 信用额度
    current_balance = db.Column(Numeric(15, 2), default=0)  # 当前余额
    
    # 备注
    description = db.Column(db.Text)
    
    def __repr__(self):
        return f'<Company {self.name}>'
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 转换Decimal类型
        if data.get('credit_limit'):
            data['credit_limit'] = float(data['credit_limit'])
        if data.get('current_balance'):
            data['current_balance'] = float(data['current_balance'])
        return data
    
    @property
    def is_active(self):
        """检查企业是否活跃"""
        return self.status == 'active' and not self.is_deleted
    
    @property
    def available_credit(self):
        """可用信用额度"""
        return self.credit_limit - abs(self.current_balance) if self.current_balance < 0 else self.credit_limit
    
    def update_balance(self, amount, description=None):
        """更新余额"""
        self.current_balance += amount
        db.session.commit()
        
        # 记录余额变动日志
        from .finance import BalanceLog
        log = BalanceLog(
            company_id=self.id,
            amount=amount,
            balance_after=self.current_balance,
            description=description or '余额变动'
        )
        log.save()
    
    @classmethod
    def get_by_code(cls, code):
        """根据企业代码获取企业"""
        return cls.query.filter_by(code=code, is_deleted=False).first()
    
    @classmethod
    def get_active_companies(cls):
        """获取所有活跃企业"""
        return cls.query.filter_by(status='active', is_deleted=False).all()
