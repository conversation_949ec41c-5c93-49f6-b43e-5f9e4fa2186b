#!/usr/bin/env python3
"""
安装缺失的依赖包
"""

import subprocess
import sys

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("安装缺失的依赖包")
    print("=" * 50)
    
    # 必需的包列表
    required_packages = [
        "marshmallow-sqlalchemy==0.29.0",
        "marshmallow==3.20.1",
    ]
    
    # 可选的包列表（如果安装失败不影响基本功能）
    optional_packages = [
        "redis==4.6.0",
        "celery==5.3.1",
        "gunicorn==21.2.0",
    ]
    
    success_count = 0
    total_count = len(required_packages)
    
    # 安装必需的包
    print("\n安装必需的包:")
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    # 安装可选的包
    print("\n安装可选的包:")
    for package in optional_packages:
        install_package(package)  # 不计入成功计数
    
    print("\n" + "=" * 50)
    if success_count == total_count:
        print("✓ 所有必需的包安装成功！")
        print("\n现在可以重新启动应用:")
        print("python run.py")
    else:
        print(f"✗ {total_count - success_count} 个必需的包安装失败")
        print("请检查网络连接或手动安装")
    print("=" * 50)

if __name__ == '__main__':
    main()
