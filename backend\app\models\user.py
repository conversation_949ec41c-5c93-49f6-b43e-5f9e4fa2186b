from werkzeug.security import generate_password_hash, check_password_hash
from flask_jwt_extended import create_access_token, create_refresh_token
from app import db
from .base import BaseModel

class User(BaseModel):
    """用户模型"""
    __tablename__ = 'users'
    
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    real_name = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    avatar = db.Column(db.String(255))
    
    # 角色和权限
    role = db.Column(db.String(20), default='user', nullable=False)  # admin, manager, user
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    is_verified = db.Column(db.<PERSON>, default=False, nullable=False)
    
    # 关联企业
    company_id = db.Column(db.In<PERSON>ger, db.ForeignKey('companies.id'))
    company = db.relationship('Company', backref='users')
    
    # 最后登录时间
    last_login = db.Column(db.DateTime)
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def generate_tokens(self):
        """生成JWT令牌"""
        access_token = create_access_token(
            identity=str(self.id),  # 转换为字符串
            additional_claims={
                'username': self.username,
                'role': self.role,
                'company_id': self.company_id
            }
        )
        refresh_token = create_refresh_token(identity=str(self.id))  # 转换为字符串
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token
        }
    
    def has_permission(self, permission):
        """检查用户权限"""
        role_permissions = {
            'admin': ['all'],
            'manager': ['warehouse', 'transport', 'delivery', 'finance'],
            'user': ['warehouse', 'transport']
        }
        
        user_permissions = role_permissions.get(self.role, [])
        return 'all' in user_permissions or permission in user_permissions
    
    def to_dict(self):
        """转换为字典，排除敏感信息"""
        data = super().to_dict()
        data.pop('password_hash', None)
        return data
    
    @classmethod
    def authenticate(cls, username, password):
        """用户认证"""
        user = cls.query.filter_by(username=username, is_deleted=False).first()
        if user and user.check_password(password) and user.is_active:
            return user
        return None
