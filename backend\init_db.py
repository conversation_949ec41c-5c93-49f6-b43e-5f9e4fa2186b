#!/usr/bin/env python3
"""
数据库初始化脚本
"""
import os
import sys
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db

# 导入所有模型
from app.models.user import User
from app.models.company import Company
from app.models.warehouse import Warehouse
from app.models.goods import Goods, Inventory
from app.models.vehicle import Vehicle
from app.models.driver import Driver
from app.models.order import Order, OrderItem
from app.models.delivery import Delivery
from app.models.finance import Bill, Payment

def init_database():
    """初始化数据库"""
    print("开始初始化数据库...")
    
    app = create_app('development')
    
    with app.app_context():
        try:
            # 删除所有表（如果存在）
            print("删除现有表...")
            db.drop_all()
            
            # 创建所有表
            print("创建数据库表...")
            db.create_all()
            
            # 创建默认数据
            create_default_data()
            
            print("✓ 数据库初始化完成！")
            return True
            
        except Exception as e:
            print(f"✗ 数据库初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def create_default_data():
    """创建默认数据"""
    print("创建默认数据...")
    
    # 创建默认企业
    company = Company(
        name='示例物流公司',
        code='DEMO001',
        type='logistics',
        contact_person='张三',
        contact_phone='***********',
        contact_email='<EMAIL>',
        address='北京市朝阳区示例路123号',
        business_license='91110000000000000X',
        legal_person='张三',
        status='active',
        entry_date=date.today(),
        credit_limit=1000000.00,
        current_balance=0.00,
        description='示例物流企业'
    )
    company.save()
    print("✓ 创建默认企业")
    
    # 创建管理员用户
    admin_user = User(
        username='admin',
        email='<EMAIL>',
        real_name='系统管理员',
        phone='***********',
        role='admin',
        is_active=True,
        is_verified=True,
        company_id=company.id
    )
    admin_user.set_password('admin123')
    admin_user.save()
    print("✓ 创建管理员用户 (用户名: admin, 密码: admin123)")
    
    # 创建普通用户
    user = User(
        username='user',
        email='<EMAIL>',
        real_name='普通用户',
        phone='***********',
        role='user',
        is_active=True,
        is_verified=True,
        company_id=company.id
    )
    user.set_password('user123')
    user.save()
    print("✓ 创建普通用户 (用户名: user, 密码: user123)")
    
    # 创建示例仓库
    warehouse = Warehouse(
        name='1号仓库',
        code='WH001',
        type='normal',
        area=1000.0,
        max_capacity=500.0,
        current_capacity=0.0,
        location='A区1号',
        height=5.0,
        volume=5000.0,
        status='active',
        temperature_min=15.0,
        temperature_max=25.0,
        humidity_min=40.0,
        humidity_max=60.0,
        company_id=company.id,
        manager_id=admin_user.id,
        rent_per_sqm=50.00,
        service_fee=1000.00,
        description='通用货物仓库'
    )
    warehouse.save()
    print("✓ 创建示例仓库")
    
    # 创建示例货物
    goods = Goods(
        name='示例商品A',
        code='GOODS001',
        category='电子产品',
        brand='示例品牌',
        model='A001',
        unit='件',
        weight=1.5,
        volume=0.01,
        length=30.0,
        width=20.0,
        height=10.0,
        storage_type='normal',
        temperature_min=15.0,
        temperature_max=25.0,
        humidity_min=40.0,
        humidity_max=60.0,
        unit_price=150.00,
        currency='CNY',
        company_id=company.id,
        status='active',
        description='示例电子产品'
    )
    goods.save()
    print("✓ 创建示例货物")
    
    # 创建库存记录
    inventory = Inventory(
        warehouse_id=warehouse.id,
        goods_id=goods.id,
        quantity=100,
        reserved_quantity=0,
        location='A-01-01',
        batch_number='BATCH001',
        production_date=date.today(),
        expiry_date=None,
        status='normal'
    )
    inventory.save()
    print("✓ 创建库存记录")
    
    # 创建示例车辆
    vehicle = Vehicle(
        license_plate='京A12345',
        vehicle_type='truck',
        brand='东风',
        model='天龙',
        color='白色',
        year=2020,
        load_capacity=10.0,
        volume_capacity=50.0,
        length=12.0,
        width=2.5,
        height=3.8,
        fuel_type='diesel',
        fuel_consumption=35.0,
        emission_standard='国六',
        registration_number='REG123456',
        insurance_number='INS789012',
        insurance_expiry=date(2025, 12, 31),
        inspection_expiry=date(2025, 6, 30),
        company_id=company.id,
        status='available',
        mileage=50000.0,
        last_maintenance=date(2024, 12, 1),
        next_maintenance=date(2025, 6, 1),
        maintenance_mileage=10000.0
    )
    vehicle.save()
    print("✓ 创建示例车辆")
    
    # 创建示例司机
    driver = Driver(
        name='李四',
        phone='13800138003',
        id_card='110101199001011234',
        gender='男',
        birth_date=date(1990, 1, 1),
        address='北京市朝阳区',
        license_number='110101199001011234',
        license_type='A2',
        license_issue_date=date(2010, 1, 1),
        license_expiry_date=date(2026, 1, 1),
        qualification_number='QUAL001',
        qualification_expiry=date(2025, 12, 31),
        company_id=company.id,
        employee_number='EMP001',
        hire_date=date.today(),
        salary_type='monthly',
        base_salary=8000.00,
        status='available',
        total_mileage=100000.0,
        total_trips=500,
        safety_score=95.0
    )
    driver.save()
    print("✓ 创建示例司机")
    
    print("✓ 默认数据创建完成")

if __name__ == '__main__':
    success = init_database()
    if success:
        print("\n数据库初始化成功！")
        print("您可以使用以下账户登录系统：")
        print("管理员 - 用户名: admin, 密码: admin123")
        print("普通用户 - 用户名: user, 密码: user123")
    else:
        print("\n数据库初始化失败！")
        sys.exit(1)
