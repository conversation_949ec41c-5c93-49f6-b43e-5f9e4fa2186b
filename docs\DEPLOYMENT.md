# 物流园区信息系统部署指南

## 系统要求

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 20GB以上可用空间
- 网络: 稳定的网络连接

### 软件要求
- 操作系统: Linux (推荐 Ubuntu 20.04+, CentOS 7+)
- Docker: 20.10+
- Docker Compose: 1.29+

## 快速部署

### 1. 获取源码
```bash
git clone <repository-url>
cd logistics_park_system
```

### 2. 一键启动
```bash
chmod +x scripts/start.sh
./scripts/start.sh
```

### 3. 访问系统
- 前端地址: http://localhost:8080
- 默认账号: admin / admin123

## 详细部署步骤

### 1. 环境准备

#### 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker
```

#### 安装Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置环境变量

创建 `.env` 文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，修改以下配置：
```env
# 数据库配置
POSTGRES_DB=logistics_park
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password

# 应用配置
FLASK_ENV=production
SECRET_KEY=your_secret_key
JWT_SECRET_KEY=your_jwt_secret_key

# Redis配置
REDIS_PASSWORD=your_redis_password
```

### 3. 构建和启动服务

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 4. 初始化数据库

```bash
# 进入后端容器
docker-compose exec backend bash

# 初始化数据库
flask init-db

# 创建管理员用户
flask create-admin
```

## 生产环境配置

### 1. 安全配置

#### 修改默认密码
- 数据库密码
- Redis密码
- 管理员账号密码
- JWT密钥

#### 配置HTTPS
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. 性能优化

#### 数据库优化
```sql
-- 在PostgreSQL中执行
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
SELECT pg_reload_conf();
```

#### Redis优化
```redis
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. 监控配置

#### 日志管理
```bash
# 查看应用日志
docker-compose logs -f backend

# 查看Nginx日志
docker-compose logs -f nginx

# 查看数据库日志
docker-compose logs -f postgres
```

#### 健康检查
```bash
# 检查服务状态
curl http://localhost:8080/api/health

# 检查数据库连接
docker-compose exec postgres pg_isready -U postgres
```

## 备份与恢复

### 数据库备份
```bash
# 创建备份
docker-compose exec postgres pg_dump -U postgres logistics_park > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复备份
docker-compose exec -T postgres psql -U postgres logistics_park < backup_file.sql
```

### 文件备份
```bash
# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz backend/uploads/

# 恢复上传文件
tar -xzf uploads_backup.tar.gz
```

## 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看容器日志
docker-compose logs [service_name]

# 检查端口占用
netstat -tlnp | grep :5000
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U postgres

# 重启数据库服务
docker-compose restart postgres
```

#### 3. 前端页面无法访问
```bash
# 检查Nginx配置
docker-compose exec nginx nginx -t

# 重启Nginx
docker-compose restart nginx
```

### 性能问题

#### 1. 响应速度慢
- 检查数据库查询性能
- 优化索引
- 增加缓存

#### 2. 内存使用过高
- 调整Docker容器内存限制
- 优化应用代码
- 配置Redis内存策略

## 升级指南

### 1. 备份数据
```bash
./scripts/backup.sh
```

### 2. 更新代码
```bash
git pull origin main
```

### 3. 重新构建
```bash
docker-compose build --no-cache
docker-compose up -d
```

### 4. 数据库迁移
```bash
docker-compose exec backend flask db upgrade
```

## 维护建议

### 定期维护任务
- 每日：检查系统日志和性能指标
- 每周：清理临时文件和日志
- 每月：更新系统补丁和依赖
- 每季度：进行完整备份和恢复测试

### 监控指标
- CPU和内存使用率
- 磁盘空间使用情况
- 数据库连接数
- API响应时间
- 错误日志数量

## 技术支持

如遇到问题，请提供以下信息：
- 系统版本和环境信息
- 错误日志和截图
- 复现步骤
- 系统配置文件

联系方式：
- 邮箱: <EMAIL>
- 文档: https://docs.logistics.com
