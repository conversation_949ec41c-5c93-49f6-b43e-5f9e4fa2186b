from app import create_app, db

# 确保所有模型都被导入
from app.models.user import User
from app.models.company import Company
from app.models.warehouse import Warehouse
from app.models.goods import Goods, Inventory
from app.models.vehicle import Vehicle
from app.models.driver import Driver
from app.models.order import Order, OrderItem
from app.models.delivery import Delivery
from app.models.finance import Bill, Payment

app = create_app()

@app.shell_context_processor
def make_shell_context():
    """为flask shell命令提供上下文"""
    return {
        'db': db,
        'User': User,
        'Company': Company,
        'Warehouse': Warehouse,
        'Goods': Goods,
        'Inventory': Inventory,
        'Vehicle': Vehicle,
        'Driver': Driver,
        'Order': Order,
        'OrderItem': OrderItem,
        'Delivery': Delivery,
        'Bill': Bill,
        'Payment': Payment
    }

@app.cli.command()
def init_db():
    """初始化数据库"""
    db.create_all()
    print('Database initialized.')

@app.cli.command()
def create_admin():
    """创建管理员用户"""
    from app.models.user import User
    from werkzeug.security import generate_password_hash
    
    admin = User(
        username='admin',
        email='<EMAIL>',
        password_hash=generate_password_hash('admin123'),
        role='admin',
        is_active=True
    )
    
    db.session.add(admin)
    db.session.commit()
    print('Admin user created.')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
