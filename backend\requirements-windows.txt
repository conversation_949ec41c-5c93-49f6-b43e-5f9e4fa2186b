# Windows 环境专用依赖文件
# 如果psycopg2-binary安装失败，可以使用这个文件

Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
Flask-Marshmallow==0.15.0
marshmallow-sqlalchemy==0.29.0

# PostgreSQL驱动 - 多个选项，按优先级排序
# 选项1: 推荐的二进制包
psycopg2-binary==2.9.7

# 如果上面失败，可以尝试以下替代方案：
# 选项2: 较旧版本的psycopg2-binary
# psycopg2-binary==2.9.5

# 选项3: 新的psycopg驱动（推荐用于新项目）
# psycopg[binary]==3.1.10

# 选项4: SQLite驱动（开发测试用）
# 如果PostgreSQL驱动都无法安装，可以临时使用SQLite
# 无需额外安装，Python内置

redis==4.6.0
celery==5.3.1
python-dotenv==1.0.0
Werkzeug==2.3.7
gunicorn==21.2.0
pytest==7.4.2
pytest-flask==1.2.0
faker==19.6.2
