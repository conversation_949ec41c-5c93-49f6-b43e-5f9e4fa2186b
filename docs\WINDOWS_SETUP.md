# Windows 环境安装指南

## 问题说明

在 Windows 环境下安装 `psycopg2-binary` 时可能遇到编译错误，这是因为缺少 Microsoft Visual C++ 编译工具。

## 解决方案

### 方案一：使用自动安装脚本（推荐）

1. 打开命令提示符（以管理员身份运行）
2. 进入项目目录：
   ```cmd
   cd logistics_park_system
   ```
3. 运行安装脚本：
   ```cmd
   scripts\install_windows.bat
   ```

### 方案二：手动安装 Visual C++ Build Tools

1. 下载并安装 Microsoft C++ Build Tools：
   https://visualstudio.microsoft.com/visual-cpp-build-tools/

2. 安装完成后重新运行：
   ```cmd
   pip install -r backend/requirements.txt
   ```

### 方案三：使用替代的 PostgreSQL 驱动

如果仍然无法安装 `psycopg2-binary`，可以尝试以下替代方案：

#### 选项1：使用较旧版本
```cmd
pip install psycopg2-binary==2.9.5 --only-binary=psycopg2-binary
```

#### 选项2：使用新的 psycopg 驱动
```cmd
pip install "psycopg[binary]==3.1.10"
```

然后需要修改代码中的导入语句：
```python
# 将所有的
import psycopg2
# 改为
import psycopg as psycopg2
```

### 方案四：使用 SQLite 进行开发测试

如果只是想快速体验系统，可以使用 SQLite 数据库：

1. 使用 SQLite 配置文件：
   ```cmd
   copy backend\config\config_sqlite.py backend\config\config.py
   ```

2. 安装依赖（不包含 PostgreSQL 驱动）：
   ```cmd
   pip install Flask==2.3.3 Flask-SQLAlchemy==3.0.5 Flask-Migrate==4.0.5 Flask-JWT-Extended==4.5.3 Flask-CORS==4.0.0 Flask-Marshmallow==0.15.0 marshmallow-sqlalchemy==0.29.0 redis==4.6.0 celery==5.3.1 python-dotenv==1.0.0 Werkzeug==2.3.7 pytest==7.4.2 pytest-flask==1.2.0 faker==19.6.2
   ```

3. 初始化数据库：
   ```cmd
   cd backend
   python run.py init-db
   python run.py create-admin
   ```

## 推荐的开发环境设置

### 1. 创建虚拟环境
```cmd
python -m venv venv
venv\Scripts\activate
```

### 2. 升级 pip
```cmd
python -m pip install --upgrade pip
```

### 3. 安装依赖
```cmd
# 尝试标准安装
pip install -r backend/requirements.txt

# 如果失败，使用 Windows 专用配置
pip install -r backend/requirements-windows.txt
```

### 4. 设置环境变量
创建 `.env` 文件：
```env
FLASK_ENV=development
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret-key
DATABASE_URL=sqlite:///logistics_park.db
```

### 5. 启动应用
```cmd
cd backend
python run.py
```

## Docker 方式（强烈推荐）

如果遇到依赖安装问题，建议使用 Docker 方式：

### 前提条件
- 安装 Docker Desktop for Windows
- 启用 WSL 2（推荐）

### 启动步骤
```cmd
# 进入项目目录
cd logistics_park_system

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

访问地址：http://localhost:8080

## 常见问题解决

### 1. Python 版本问题
确保使用 Python 3.8 或更高版本：
```cmd
python --version
```

### 2. pip 版本过旧
```cmd
python -m pip install --upgrade pip
```

### 3. 网络问题
如果下载速度慢，可以使用国内镜像：
```cmd
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 4. 权限问题
以管理员身份运行命令提示符

### 5. 端口占用
如果 5000 端口被占用，可以修改 `run.py` 中的端口号：
```python
app.run(debug=True, host='0.0.0.0', port=5001)
```

## 开发建议

1. **使用 Docker**：避免环境配置问题
2. **虚拟环境**：隔离项目依赖
3. **SQLite 开发**：快速原型开发
4. **PostgreSQL 生产**：生产环境使用

## 技术支持

如果仍然遇到问题，请提供以下信息：
- Windows 版本
- Python 版本
- 完整的错误信息
- 已尝试的解决方案

联系方式：
- 创建 GitHub Issue
- 发送邮件至技术支持
