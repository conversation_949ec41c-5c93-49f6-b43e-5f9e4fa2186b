from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.finance import Bill, Payment
from app.models.user import User

finance_bp = Blueprint('finance', __name__)

# 权限检查装饰器
def require_permission(permission):
    def decorator(f):
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.get_by_id(current_user_id)
            if not user or not user.has_permission(permission):
                return jsonify({'error': '权限不足'}), 403
            return f(*args, **kwargs)
        decorated_function.__name__ = f.__name__
        return decorated_function
    return decorator

@finance_bp.route('/bills', methods=['GET'])
@jwt_required()
@require_permission('finance')
def get_bills():
    """获取账单列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    status = request.args.get('status')
    company_id = request.args.get('company_id', type=int)
    bill_type = request.args.get('type')
    
    query = Bill.query.filter_by(is_deleted=False)
    
    if status:
        query = query.filter_by(status=status)
    if company_id:
        query = query.filter_by(company_id=company_id)
    if bill_type:
        query = query.filter_by(bill_type=bill_type)
    
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'bills': [bill.to_dict() for bill in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })

@finance_bp.route('/payments', methods=['GET'])
@jwt_required()
@require_permission('finance')
def get_payments():
    """获取支付记录"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    pagination = Payment.paginate(page=page, per_page=per_page)
    
    return jsonify({
        'payments': [payment.to_dict() for payment in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })
