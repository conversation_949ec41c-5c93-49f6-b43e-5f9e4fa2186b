from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    create_access_token, create_refresh_token, jwt_required,
    get_jwt_identity, get_jwt
)
from app import db
from app.models.user import User
from app.models.company import Company
from datetime import datetime, timedelta

# 简单的验证函数，不依赖Marshmallow
def validate_login_data(data):
    """验证登录数据"""
    if not data:
        return False, "请求数据为空"

    username = data.get('username', '').strip()
    password = data.get('password', '').strip()

    if not username:
        return False, "用户名不能为空"
    if len(username) < 3 or len(username) > 80:
        return False, "用户名长度应在3-80个字符之间"

    if not password:
        return False, "密码不能为空"
    if len(password) < 6:
        return False, "密码长度不能少于6个字符"

    return True, {"username": username, "password": password}

def validate_register_data(data):
    """验证注册数据"""
    if not data:
        return False, "请求数据为空"

    username = data.get('username', '').strip()
    email = data.get('email', '').strip()
    password = data.get('password', '').strip()

    if not username:
        return False, "用户名不能为空"
    if len(username) < 3 or len(username) > 80:
        return False, "用户名长度应在3-80个字符之间"

    if not email:
        return False, "邮箱不能为空"
    if '@' not in email:
        return False, "邮箱格式不正确"

    if not password:
        return False, "密码不能为空"
    if len(password) < 6:
        return False, "密码长度不能少于6个字符"

    return True, {
        "username": username,
        "email": email,
        "password": password,
        "real_name": data.get('real_name', '').strip(),
        "phone": data.get('phone', '').strip(),
        "company_id": data.get('company_id')
    }

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    is_valid, result = validate_login_data(request.json)
    if not is_valid:
        return jsonify({'error': result}), 400

    data = result
    
    user = User.authenticate(data['username'], data['password'])
    if not user:
        return jsonify({'error': '用户名或密码错误'}), 401
    
    if not user.is_active:
        return jsonify({'error': '账户已被禁用'}), 401
    
    # 更新最后登录时间
    user.last_login = datetime.utcnow()
    db.session.commit()
    
    # 生成令牌
    tokens = user.generate_tokens()
    
    return jsonify({
        'message': '登录成功',
        'user': user.to_dict(),
        'tokens': tokens
    })

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    is_valid, result = validate_register_data(request.json)
    if not is_valid:
        return jsonify({'error': result}), 400

    data = result
    
    # 检查用户名是否已存在
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'error': '用户名已存在'}), 400
    
    # 检查邮箱是否已存在
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'error': '邮箱已存在'}), 400
    
    # 验证企业ID（如果提供）
    company = None
    if data.get('company_id'):
        company = Company.get_by_id(data['company_id'])
        if not company or not company.is_active:
            return jsonify({'error': '企业不存在或未激活'}), 400
    
    # 创建用户
    user = User(
        username=data['username'],
        email=data['email'],
        real_name=data.get('real_name'),
        phone=data.get('phone'),
        company_id=data.get('company_id'),
        role='user'  # 默认角色
    )
    user.set_password(data['password'])
    
    try:
        user.save()
        return jsonify({
            'message': '注册成功',
            'user': user.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '注册失败，请稍后重试'}), 500

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """刷新访问令牌"""
    try:
        current_user_id = get_jwt_identity()
        print(f"刷新令牌请求，用户ID: {current_user_id}")

        user = User.get_by_id(current_user_id)

        if not user:
            print(f"用户不存在: {current_user_id}")
            return jsonify({'error': '用户不存在'}), 401

        if not user.is_active:
            print(f"用户已被禁用: {user.username}")
            return jsonify({'error': '用户已被禁用'}), 401

        # 生成新的访问令牌
        access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'username': user.username,
                'role': user.role,
                'company_id': user.company_id
            }
        )

        print(f"成功为用户 {user.username} 刷新令牌")
        return jsonify({
            'access_token': access_token
        })

    except Exception as e:
        print(f"刷新令牌时出错: {str(e)}")
        return jsonify({'error': '刷新令牌失败'}), 401

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    # 在实际应用中，可以将令牌加入黑名单
    return jsonify({'message': '登出成功'})

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户资料"""
    current_user_id = get_jwt_identity()
    user = User.get_by_id(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    profile = user.to_dict()
    if user.company:
        profile['company'] = user.company.to_dict()
    
    return jsonify(profile)

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户资料"""
    try:
        data = update_profile_schema.load(request.json)
    except ValidationError as err:
        return jsonify({'error': '输入数据无效', 'details': err.messages}), 400
    
    current_user_id = get_jwt_identity()
    user = User.get_by_id(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 更新用户信息
    for key, value in data.items():
        if hasattr(user, key):
            setattr(user, key, value)
    
    try:
        db.session.commit()
        return jsonify({
            'message': '资料更新成功',
            'user': user.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '更新失败，请稍后重试'}), 500

@auth_bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """修改密码"""
    try:
        data = change_password_schema.load(request.json)
    except ValidationError as err:
        return jsonify({'error': '输入数据无效', 'details': err.messages}), 400
    
    current_user_id = get_jwt_identity()
    user = User.get_by_id(current_user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 验证旧密码
    if not user.check_password(data['old_password']):
        return jsonify({'error': '原密码错误'}), 400
    
    # 设置新密码
    user.set_password(data['new_password'])
    
    try:
        db.session.commit()
        return jsonify({'message': '密码修改成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '密码修改失败，请稍后重试'}), 500

@auth_bp.route('/verify-token', methods=['POST'])
@jwt_required()
def verify_token():
    """验证令牌有效性"""
    current_user_id = get_jwt_identity()
    claims = get_jwt()
    
    user = User.get_by_id(current_user_id)
    if not user or not user.is_active:
        return jsonify({'error': '令牌无效'}), 401
    
    return jsonify({
        'valid': True,
        'user_id': current_user_id,
        'username': claims.get('username'),
        'role': claims.get('role'),
        'company_id': claims.get('company_id')
    })

@auth_bp.route('/permissions', methods=['GET'])
@jwt_required()
def get_permissions():
    """获取用户权限"""
    current_user_id = get_jwt_identity()
    # 将字符串ID转换为整数
    user = User.get_by_id(int(current_user_id))
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 根据角色返回权限列表
    role_permissions = {
        'admin': [
            'admin', 'warehouse', 'transport:view', 'transport:vehicle:view',
            'transport:driver:view', 'transport:order:view', 'delivery', 'finance'
        ],
        'manager': [
            'warehouse', 'transport:view', 'transport:vehicle:view',
            'transport:driver:view', 'transport:order:view', 'delivery', 'finance'
        ],
        'user': [
            'warehouse', 'transport:view', 'delivery'
        ]
    }
    
    permissions = role_permissions.get(user.role, [])
    
    return jsonify({
        'role': user.role,
        'permissions': permissions
    })
