from app import db
from .base import BaseModel
from sqlalchemy import Numeric

class Vehicle(BaseModel):
    """车辆模型"""
    __tablename__ = 'vehicles'
    
    # 基本信息
    license_plate = db.Column(db.String(20), unique=True, nullable=False, index=True)
    vehicle_type = db.Column(db.String(20), nullable=False)  # truck, van, container
    brand = db.Column(db.String(50))
    model = db.Column(db.String(50))
    color = db.Column(db.String(20))
    year = db.Column(db.Integer)
    
    # 规格信息
    load_capacity = db.Column(db.Float)  # 载重量(吨)
    volume_capacity = db.Column(db.Float)  # 容积(立方米)
    length = db.Column(db.Float)  # 长度(米)
    width = db.Column(db.Float)   # 宽度(米)
    height = db.Column(db.Float)  # 高度(米)
    
    # 技术参数
    fuel_type = db.Column(db.String(20))  # diesel, gasoline, electric, hybrid
    fuel_consumption = db.Column(db.Float)  # 油耗(L/100km)
    emission_standard = db.Column(db.String(20))  # 排放标准
    
    # 证件信息
    registration_number = db.Column(db.String(50))  # 行驶证号
    insurance_number = db.Column(db.String(50))     # 保险单号
    insurance_expiry = db.Column(db.Date)           # 保险到期日
    inspection_expiry = db.Column(db.Date)          # 年检到期日
    
    # 关联信息
    company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=False)
    company = db.relationship('Company', backref='vehicles')
    
    current_driver_id = db.Column(db.Integer, db.ForeignKey('drivers.id'))
    current_driver = db.relationship('Driver', backref='current_vehicles')
    
    # 状态信息
    status = db.Column(db.String(20), default='available')  # available, in_use, maintenance, inactive
    location = db.Column(db.String(200))  # 当前位置
    mileage = db.Column(db.Float, default=0)  # 总里程
    
    # 维护信息
    last_maintenance = db.Column(db.Date)  # 最后维护日期
    next_maintenance = db.Column(db.Date)  # 下次维护日期
    maintenance_mileage = db.Column(db.Float)  # 维护里程间隔
    
    # 费用信息
    purchase_price = db.Column(Numeric(12, 2))  # 购买价格
    daily_rent = db.Column(Numeric(8, 2))       # 日租金
    km_rate = db.Column(Numeric(6, 2))          # 公里费率
    
    # 备注
    description = db.Column(db.Text)
    
    def __repr__(self):
        return f'<Vehicle {self.license_plate}>'
    
    @property
    def is_available(self):
        """是否可用"""
        return self.status == 'available' and not self.is_deleted
    
    @property
    def needs_maintenance(self):
        """是否需要维护"""
        if self.next_maintenance:
            from datetime import date
            return date.today() >= self.next_maintenance
        return False
    
    @property
    def insurance_expired(self):
        """保险是否过期"""
        if self.insurance_expiry:
            from datetime import date
            return date.today() > self.insurance_expiry
        return False
    
    @property
    def inspection_expired(self):
        """年检是否过期"""
        if self.inspection_expiry:
            from datetime import date
            return date.today() > self.inspection_expiry
        return False
    
    def update_mileage(self, new_mileage):
        """更新里程"""
        if new_mileage < self.mileage:
            raise ValueError("新里程不能小于当前里程")
        
        self.mileage = new_mileage
        
        # 检查是否需要维护
        if self.maintenance_mileage and self.last_maintenance:
            if new_mileage - self.last_maintenance >= self.maintenance_mileage:
                self.status = 'maintenance'
        
        db.session.commit()
    
    def assign_driver(self, driver_id):
        """分配司机"""
        if self.status != 'available':
            raise ValueError("车辆不可用，无法分配司机")
        
        self.current_driver_id = driver_id
        self.status = 'in_use'
        db.session.commit()
    
    def release_driver(self):
        """释放司机"""
        self.current_driver_id = None
        self.status = 'available'
        db.session.commit()
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 添加计算属性
        data['is_available'] = self.is_available
        data['needs_maintenance'] = self.needs_maintenance
        data['insurance_expired'] = self.insurance_expired
        data['inspection_expired'] = self.inspection_expired
        
        # 转换Decimal类型
        decimal_fields = ['purchase_price', 'daily_rent', 'km_rate']
        for field in decimal_fields:
            if data.get(field):
                data[field] = float(data[field])
        
        return data
    
    @classmethod
    def get_available_vehicles(cls, company_id=None, vehicle_type=None):
        """获取可用车辆"""
        query = cls.query.filter_by(status='available', is_deleted=False)
        if company_id:
            query = query.filter_by(company_id=company_id)
        if vehicle_type:
            query = query.filter_by(vehicle_type=vehicle_type)
        return query.all()
    
    @classmethod
    def get_by_license_plate(cls, license_plate):
        """根据车牌号获取车辆"""
        return cls.query.filter_by(license_plate=license_plate, is_deleted=False).first()
