from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import J<PERSON>TManager
from flask_cors import CORS

# 尝试导入 Marshmallow，如果失败则提供警告
try:
    from flask_marshmallow import Marshmallow
    MARSHMALLOW_AVAILABLE = True
except ImportError:
    print("警告: flask-marshmallow 或 marshmallow-sqlalchemy 未安装")
    print("请运行: pip install marshmallow-sqlalchemy")
    MARSHMALLOW_AVAILABLE = False
    Marshmallow = None
from config.config import config
import os

# 初始化扩展
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()

# 条件初始化 Marshmallow
if MARSHMALLOW_AVAILABLE:
    ma = Marshmallow()
else:
    ma = None

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)

    # 初始化Marshmallow（如果可用）
    if MARSHMALLOW_AVAILABLE and ma:
        ma.init_app(app)
    
    # 配置CORS
    CORS(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:3000", "http://localhost:5173"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # 注册蓝图
    from app.views.auth import auth_bp
    from app.views.warehouse import warehouse_bp
    from app.views.transport import transport_bp
    from app.views.delivery import delivery_bp
    from app.views.finance import finance_bp
    from app.views.dashboard import dashboard_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(warehouse_bp, url_prefix='/api/warehouse')
    app.register_blueprint(transport_bp, url_prefix='/api/transport')
    app.register_blueprint(delivery_bp, url_prefix='/api/delivery')
    app.register_blueprint(finance_bp, url_prefix='/api/finance')
    app.register_blueprint(dashboard_bp, url_prefix='/api/dashboard')

    # 根路径路由
    @app.route('/')
    def index():
        """API根路径，返回API信息"""
        return {
            'message': '物流园区管理系统 API',
            'version': '1.0.0',
            'status': 'running',
            'endpoints': {
                'auth': '/api/auth',
                'warehouse': '/api/warehouse',
                'transport': '/api/transport',
                'delivery': '/api/delivery',
                'finance': '/api/finance',
                'dashboard': '/api/dashboard'
            },
            'documentation': '/api/docs'
        }

    @app.route('/health')
    @app.route('/api/health')
    def health_check():
        """健康检查端点"""
        from datetime import datetime
        return {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': '物流园区管理系统',
            'version': '1.0.0'
        }

    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return {'error': 'Not found'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return {'error': 'Internal server error'}, 500
    
    return app
