from app import db
from .base import BaseModel
from sqlalchemy import Numeric

class Warehouse(BaseModel):
    """仓库模型"""
    __tablename__ = 'warehouses'
    
    # 基本信息
    name = db.Column(db.String(100), nullable=False, index=True)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    type = db.Column(db.String(20), nullable=False)  # normal, cold, dangerous
    
    # 位置信息
    location = db.Column(db.String(200))
    area = db.Column(db.Float)  # 面积(平方米)
    height = db.Column(db.Float)  # 高度(米)
    volume = db.Column(db.Float)  # 体积(立方米)
    
    # 容量信息
    max_capacity = db.Column(db.Float)  # 最大容量
    current_capacity = db.Column(db.Float, default=0)  # 当前使用容量
    
    # 状态信息
    status = db.Column(db.String(20), default='active')  # active, maintenance, inactive
    temperature_min = db.Column(db.Float)  # 最低温度
    temperature_max = db.Column(db.Float)  # 最高温度
    humidity_min = db.Column(db.Float)  # 最低湿度
    humidity_max = db.Column(db.Float)  # 最高湿度
    
    # 关联企业
    company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=False)
    company = db.relationship('Company', backref='warehouses')
    
    # 管理员
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    manager = db.relationship('User', backref='managed_warehouses')
    
    # 费用信息
    rent_per_sqm = db.Column(Numeric(10, 2))  # 每平米租金
    service_fee = db.Column(Numeric(10, 2))  # 服务费
    
    # 备注
    description = db.Column(db.Text)
    
    def __repr__(self):
        return f'<Warehouse {self.name}>'
    
    @property
    def utilization_rate(self):
        """使用率"""
        if self.max_capacity and self.max_capacity > 0:
            return (self.current_capacity / self.max_capacity) * 100
        return 0
    
    @property
    def available_capacity(self):
        """可用容量"""
        return self.max_capacity - self.current_capacity if self.max_capacity else 0
    
    @property
    def is_available(self):
        """是否可用"""
        return self.status == 'active' and not self.is_deleted
    
    def update_capacity(self, change_amount):
        """更新容量使用情况"""
        new_capacity = self.current_capacity + change_amount
        if new_capacity < 0:
            raise ValueError("容量不能为负数")
        if self.max_capacity and new_capacity > self.max_capacity:
            raise ValueError("超出最大容量限制")
        
        self.current_capacity = new_capacity
        db.session.commit()
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 添加计算属性
        data['utilization_rate'] = self.utilization_rate
        data['available_capacity'] = self.available_capacity
        data['is_available'] = self.is_available
        
        # 转换Decimal类型
        if data.get('rent_per_sqm'):
            data['rent_per_sqm'] = float(data['rent_per_sqm'])
        if data.get('service_fee'):
            data['service_fee'] = float(data['service_fee'])
            
        return data
    
    @classmethod
    def get_available_warehouses(cls, company_id=None):
        """获取可用仓库"""
        query = cls.query.filter_by(status='active', is_deleted=False)
        if company_id:
            query = query.filter_by(company_id=company_id)
        return query.all()
    
    @classmethod
    def get_by_code(cls, code):
        """根据仓库代码获取仓库"""
        return cls.query.filter_by(code=code, is_deleted=False).first()
