from app import db
from .base import BaseModel
from datetime import datetime
from sqlalchemy import Numeric

class Order(BaseModel):
    """订单模型"""
    __tablename__ = 'orders'
    
    # 基本信息
    order_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    order_type = db.Column(db.String(20), nullable=False)  # transport, storage, delivery
    status = db.Column(db.String(20), default='pending')  # pending, confirmed, in_progress, completed, cancelled
    
    # 客户信息
    customer_company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=False)
    customer_company = db.relationship('Company', foreign_keys=[customer_company_id], backref='customer_orders')
    
    # 服务提供商
    service_company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=False)
    service_company = db.relationship('Company', foreign_keys=[service_company_id], backref='service_orders')
    
    # 时间信息
    order_date = db.Column(db.DateTime, default=datetime.utcnow)
    required_date = db.Column(db.DateTime)  # 要求完成时间
    start_date = db.Column(db.DateTime)     # 开始时间
    completion_date = db.Column(db.DateTime)  # 完成时间
    
    # 地址信息
    pickup_address = db.Column(db.Text)
    delivery_address = db.Column(db.Text)
    pickup_contact = db.Column(db.String(50))
    pickup_phone = db.Column(db.String(20))
    delivery_contact = db.Column(db.String(50))
    delivery_phone = db.Column(db.String(20))
    
    # 费用信息
    total_amount = db.Column(Numeric(12, 2), default=0)
    paid_amount = db.Column(Numeric(12, 2), default=0)
    currency = db.Column(db.String(10), default='CNY')
    
    # 运输信息（如果是运输订单）
    vehicle_id = db.Column(db.Integer, db.ForeignKey('vehicles.id'))
    vehicle = db.relationship('Vehicle', backref='orders')
    
    driver_id = db.Column(db.Integer, db.ForeignKey('drivers.id'))
    driver = db.relationship('Driver', backref='orders')
    
    distance = db.Column(db.Float)  # 距离(公里)
    estimated_duration = db.Column(db.Integer)  # 预计时长(分钟)
    
    # 仓储信息（如果是仓储订单）
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'))
    warehouse = db.relationship('Warehouse', backref='orders')
    
    storage_start_date = db.Column(db.Date)
    storage_end_date = db.Column(db.Date)
    
    # 优先级
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    
    # 特殊要求
    special_requirements = db.Column(db.Text)
    temperature_requirement = db.Column(db.String(50))
    handling_instructions = db.Column(db.Text)
    
    # 创建人
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', backref='created_orders')
    
    # 备注
    notes = db.Column(db.Text)
    
    def __repr__(self):
        return f'<Order {self.order_number}>'
    
    @property
    def is_overdue(self):
        """是否逾期"""
        if self.required_date and self.status not in ['completed', 'cancelled']:
            return datetime.utcnow() > self.required_date
        return False
    
    @property
    def remaining_amount(self):
        """剩余金额"""
        return self.total_amount - self.paid_amount
    
    @property
    def payment_status(self):
        """支付状态"""
        if self.paid_amount == 0:
            return 'unpaid'
        elif self.paid_amount >= self.total_amount:
            return 'paid'
        else:
            return 'partial'
    
    def update_status(self, new_status, notes=None):
        """更新订单状态"""
        valid_statuses = ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled']
        if new_status not in valid_statuses:
            raise ValueError(f"无效状态: {new_status}")
        
        old_status = self.status
        self.status = new_status
        
        if new_status == 'in_progress' and not self.start_date:
            self.start_date = datetime.utcnow()
        elif new_status == 'completed' and not self.completion_date:
            self.completion_date = datetime.utcnow()
        
        if notes:
            self.notes = (self.notes or '') + f"\n[{datetime.utcnow()}] 状态变更: {old_status} -> {new_status}. {notes}"
        
        db.session.commit()
        
        # 记录状态变更日志
        log = OrderStatusLog(
            order_id=self.id,
            old_status=old_status,
            new_status=new_status,
            notes=notes
        )
        log.save()
    
    def add_payment(self, amount, payment_method='cash', notes=None):
        """添加支付记录"""
        if amount <= 0:
            raise ValueError("支付金额必须大于0")
        
        if self.paid_amount + amount > self.total_amount:
            raise ValueError("支付金额超过订单总额")
        
        self.paid_amount += amount
        db.session.commit()
        
        # 记录支付日志
        from .finance import Payment
        payment = Payment(
            order_id=self.id,
            amount=amount,
            payment_method=payment_method,
            notes=notes
        )
        payment.save()
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 添加计算属性
        data['is_overdue'] = self.is_overdue
        data['remaining_amount'] = float(self.remaining_amount) if self.remaining_amount else 0
        data['payment_status'] = self.payment_status
        
        # 转换Decimal类型
        decimal_fields = ['total_amount', 'paid_amount']
        for field in decimal_fields:
            if data.get(field):
                data[field] = float(data[field])
        
        return data
    
    @classmethod
    def generate_order_number(cls, order_type):
        """生成订单号"""
        from datetime import datetime
        prefix_map = {
            'transport': 'TR',
            'storage': 'ST',
            'delivery': 'DL'
        }
        prefix = prefix_map.get(order_type, 'OR')
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 查找当天同类型订单数量
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        count = cls.query.filter(
            cls.order_type == order_type,
            cls.created_at >= today_start
        ).count()
        
        return f"{prefix}{timestamp}{count+1:03d}"


class OrderItem(BaseModel):
    """订单明细模型"""
    __tablename__ = 'order_items'
    
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)
    order = db.relationship('Order', backref='items')
    
    goods_id = db.Column(db.Integer, db.ForeignKey('goods.id'), nullable=False)
    goods = db.relationship('Goods', backref='order_items')
    
    quantity = db.Column(db.Float, nullable=False)
    unit_price = db.Column(Numeric(10, 2))
    total_price = db.Column(Numeric(12, 2))
    
    # 实际处理数量
    processed_quantity = db.Column(db.Float, default=0)
    
    # 特殊要求
    special_instructions = db.Column(db.Text)
    
    def __repr__(self):
        return f'<OrderItem {self.goods.name} x {self.quantity}>'
    
    @property
    def remaining_quantity(self):
        """剩余数量"""
        return self.quantity - self.processed_quantity
    
    def process_quantity(self, amount):
        """处理数量"""
        if amount > self.remaining_quantity:
            raise ValueError("处理数量超过剩余数量")
        
        self.processed_quantity += amount
        db.session.commit()
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        data['remaining_quantity'] = self.remaining_quantity
        
        # 转换Decimal类型
        decimal_fields = ['unit_price', 'total_price']
        for field in decimal_fields:
            if data.get(field):
                data[field] = float(data[field])
        
        return data


class OrderStatusLog(BaseModel):
    """订单状态变更日志"""
    __tablename__ = 'order_status_logs'
    
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)
    order = db.relationship('Order', backref='status_logs')
    
    old_status = db.Column(db.String(20), nullable=False)
    new_status = db.Column(db.String(20), nullable=False)
    
    changed_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    changed_by = db.relationship('User', backref='order_status_changes')
    
    notes = db.Column(db.Text)
    
    def __repr__(self):
        return f'<OrderStatusLog {self.old_status} -> {self.new_status}>'
