from datetime import datetime
from app import db

class BaseModel(db.Model):
    """基础模型类，包含公共字段"""
    __abstract__ = True
    
    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_deleted = db.Column(db.<PERSON><PERSON>, default=False, nullable=False)
    
    def to_dict(self):
        """转换为字典格式"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            result[column.name] = value
        return result
    
    def save(self):
        """保存到数据库"""
        db.session.add(self)
        db.session.commit()
        return self
    
    def delete(self):
        """软删除"""
        self.is_deleted = True
        db.session.commit()
    
    def hard_delete(self):
        """硬删除"""
        db.session.delete(self)
        db.session.commit()
    
    @classmethod
    def get_by_id(cls, id):
        """根据ID获取记录"""
        return cls.query.filter_by(id=id, is_deleted=False).first()
    
    @classmethod
    def get_all(cls):
        """获取所有未删除的记录"""
        return cls.query.filter_by(is_deleted=False).all()
    
    @classmethod
    def paginate(cls, page=1, per_page=20, **filters):
        """分页查询"""
        query = cls.query.filter_by(is_deleted=False)
        
        # 应用过滤条件
        for key, value in filters.items():
            if hasattr(cls, key) and value is not None:
                query = query.filter(getattr(cls, key) == value)
        
        return query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
