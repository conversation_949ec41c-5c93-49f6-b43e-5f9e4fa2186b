from app import db
from .base import BaseModel
from sqlalchemy import Numeric

class Goods(BaseModel):
    """货物模型"""
    __tablename__ = 'goods'
    
    # 基本信息
    name = db.Column(db.String(100), nullable=False, index=True)
    code = db.Column(db.String(50), unique=True, nullable=False, index=True)
    category = db.Column(db.String(50), nullable=False, index=True)
    brand = db.Column(db.String(50))
    model = db.Column(db.String(50))
    
    # 规格信息
    unit = db.Column(db.String(20), nullable=False)  # 单位：件、箱、吨等
    weight = db.Column(db.Float)  # 重量(kg)
    volume = db.Column(db.Float)  # 体积(立方米)
    length = db.Column(db.Float)  # 长度(cm)
    width = db.Column(db.Float)   # 宽度(cm)
    height = db.Column(db.Float)  # 高度(cm)
    
    # 存储要求
    storage_type = db.Column(db.String(20), default='normal')  # normal, cold, dangerous
    temperature_min = db.Column(db.Float)  # 存储最低温度
    temperature_max = db.Column(db.Float)  # 存储最高温度
    humidity_min = db.Column(db.Float)     # 存储最低湿度
    humidity_max = db.Column(db.Float)     # 存储最高湿度
    
    # 价值信息
    unit_price = db.Column(Numeric(10, 2))  # 单价
    currency = db.Column(db.String(10), default='CNY')  # 货币单位
    
    # 关联企业
    company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=False)
    company = db.relationship('Company', backref='goods')
    
    # 状态
    status = db.Column(db.String(20), default='active')  # active, inactive
    
    # 备注
    description = db.Column(db.Text)
    image_url = db.Column(db.String(255))  # 货物图片
    
    def __repr__(self):
        return f'<Goods {self.name}>'
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 转换Decimal类型
        if data.get('unit_price'):
            data['unit_price'] = float(data['unit_price'])
        return data
    
    @classmethod
    def get_by_code(cls, code):
        """根据货物代码获取货物"""
        return cls.query.filter_by(code=code, is_deleted=False).first()
    
    @classmethod
    def get_by_category(cls, category, company_id=None):
        """根据分类获取货物"""
        query = cls.query.filter_by(category=category, is_deleted=False)
        if company_id:
            query = query.filter_by(company_id=company_id)
        return query.all()


class Inventory(BaseModel):
    """库存模型"""
    __tablename__ = 'inventory'
    
    # 关联信息
    goods_id = db.Column(db.Integer, db.ForeignKey('goods.id'), nullable=False)
    goods = db.relationship('Goods', backref='inventory_records')
    
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    warehouse = db.relationship('Warehouse', backref='inventory_records')
    
    # 库存信息
    quantity = db.Column(db.Float, nullable=False, default=0)  # 库存数量
    reserved_quantity = db.Column(db.Float, default=0)  # 预留数量
    location = db.Column(db.String(50))  # 库位
    batch_number = db.Column(db.String(50))  # 批次号
    
    # 日期信息
    production_date = db.Column(db.Date)  # 生产日期
    expiry_date = db.Column(db.Date)      # 过期日期
    entry_date = db.Column(db.Date)       # 入库日期
    
    # 状态
    status = db.Column(db.String(20), default='normal')  # normal, reserved, locked, expired
    
    # 最后盘点信息
    last_check_date = db.Column(db.Date)  # 最后盘点日期
    last_check_quantity = db.Column(db.Float)  # 最后盘点数量
    
    def __repr__(self):
        return f'<Inventory {self.goods.name} - {self.quantity}>'
    
    @property
    def available_quantity(self):
        """可用数量"""
        return self.quantity - self.reserved_quantity
    
    @property
    def is_expired(self):
        """是否过期"""
        if self.expiry_date:
            from datetime import date
            return date.today() > self.expiry_date
        return False
    
    @property
    def days_to_expiry(self):
        """距离过期天数"""
        if self.expiry_date:
            from datetime import date
            delta = self.expiry_date - date.today()
            return delta.days
        return None
    
    def reserve_quantity(self, amount):
        """预留库存"""
        if amount > self.available_quantity:
            raise ValueError("预留数量超过可用库存")
        self.reserved_quantity += amount
        db.session.commit()
    
    def release_reservation(self, amount):
        """释放预留"""
        if amount > self.reserved_quantity:
            raise ValueError("释放数量超过预留数量")
        self.reserved_quantity -= amount
        db.session.commit()
    
    def update_quantity(self, change_amount, operation_type='adjust'):
        """更新库存数量"""
        new_quantity = self.quantity + change_amount
        if new_quantity < 0:
            raise ValueError("库存数量不能为负数")
        
        old_quantity = self.quantity
        self.quantity = new_quantity
        db.session.commit()
        
        # 记录库存变动日志
        log = InventoryLog(
            inventory_id=self.id,
            operation_type=operation_type,
            quantity_before=old_quantity,
            quantity_change=change_amount,
            quantity_after=new_quantity
        )
        log.save()
    
    @classmethod
    def get_by_goods_and_warehouse(cls, goods_id, warehouse_id):
        """根据货物和仓库获取库存"""
        return cls.query.filter_by(
            goods_id=goods_id,
            warehouse_id=warehouse_id,
            is_deleted=False
        ).first()
    
    @classmethod
    def get_low_stock(cls, threshold=10):
        """获取低库存商品"""
        return cls.query.filter(
            cls.quantity <= threshold,
            cls.is_deleted == False
        ).all()


class InventoryLog(BaseModel):
    """库存变动日志"""
    __tablename__ = 'inventory_logs'
    
    inventory_id = db.Column(db.Integer, db.ForeignKey('inventory.id'), nullable=False)
    inventory = db.relationship('Inventory', backref='logs')
    
    operation_type = db.Column(db.String(20), nullable=False)  # in, out, adjust, check
    quantity_before = db.Column(db.Float, nullable=False)
    quantity_change = db.Column(db.Float, nullable=False)
    quantity_after = db.Column(db.Float, nullable=False)
    
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    operator = db.relationship('User', backref='inventory_operations')
    
    reference_id = db.Column(db.String(50))  # 关联单据号
    reference_type = db.Column(db.String(20))  # 单据类型
    
    notes = db.Column(db.Text)  # 备注
    
    def __repr__(self):
        return f'<InventoryLog {self.operation_type} - {self.quantity_change}>'
