from app import db
from .base import BaseModel
from datetime import datetime
from sqlalchemy import Numeric

class Bill(BaseModel):
    """账单模型"""
    __tablename__ = 'bills'
    
    # 基本信息
    bill_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    bill_type = db.Column(db.String(20), nullable=False)  # storage, transport, service, penalty
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue, cancelled
    
    # 关联信息
    company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=False)
    company = db.relationship('Company', backref='bills')
    
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'))
    order = db.relationship('Order', backref='bills')
    
    # 金额信息
    amount = db.Column(Numeric(12, 2), nullable=False)
    tax_amount = db.Column(Numeric(10, 2), default=0)
    total_amount = db.Column(Numeric(12, 2), nullable=False)
    paid_amount = db.Column(Numeric(12, 2), default=0)
    currency = db.Column(db.String(10), default='CNY')
    
    # 时间信息
    bill_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)
    
    # 计费周期（对于周期性账单）
    billing_period_start = db.Column(db.Date)
    billing_period_end = db.Column(db.Date)
    
    # 详细信息
    description = db.Column(db.Text)
    items = db.Column(db.Text)  # JSON格式存储账单明细
    
    # 创建人
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', backref='created_bills')
    
    # 备注
    notes = db.Column(db.Text)
    
    def __repr__(self):
        return f'<Bill {self.bill_number}>'
    
    @property
    def remaining_amount(self):
        """剩余金额"""
        return self.total_amount - self.paid_amount
    
    @property
    def is_overdue(self):
        """是否逾期"""
        if self.status == 'pending' and self.due_date:
            from datetime import date
            return date.today() > self.due_date
        return False
    
    @property
    def days_overdue(self):
        """逾期天数"""
        if self.is_overdue:
            from datetime import date
            return (date.today() - self.due_date).days
        return 0
    
    @property
    def payment_status(self):
        """支付状态"""
        if self.paid_amount == 0:
            return 'unpaid'
        elif self.paid_amount >= self.total_amount:
            return 'paid'
        else:
            return 'partial'
    
    def add_payment(self, amount, payment_method='cash', reference=None, notes=None):
        """添加支付"""
        if amount <= 0:
            raise ValueError("支付金额必须大于0")
        
        if self.paid_amount + amount > self.total_amount:
            raise ValueError("支付金额超过账单总额")
        
        self.paid_amount += amount
        
        if self.paid_amount >= self.total_amount:
            self.status = 'paid'
            self.paid_date = datetime.utcnow().date()
        
        db.session.commit()
        
        # 创建支付记录
        payment = Payment(
            bill_id=self.id,
            amount=amount,
            payment_method=payment_method,
            reference=reference,
            notes=notes
        )
        payment.save()
        
        # 更新企业余额
        self.company.update_balance(-amount, f"支付账单 {self.bill_number}")
    
    def cancel(self, reason=None):
        """取消账单"""
        if self.status == 'paid':
            raise ValueError("已支付的账单无法取消")
        
        self.status = 'cancelled'
        if reason:
            self.notes = (self.notes or '') + f"\n[{datetime.utcnow()}] 账单取消: {reason}"
        
        db.session.commit()
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 添加计算属性
        data['remaining_amount'] = float(self.remaining_amount)
        data['is_overdue'] = self.is_overdue
        data['days_overdue'] = self.days_overdue
        data['payment_status'] = self.payment_status
        
        # 转换Decimal类型
        decimal_fields = ['amount', 'tax_amount', 'total_amount', 'paid_amount']
        for field in decimal_fields:
            if data.get(field):
                data[field] = float(data[field])
        
        return data
    
    @classmethod
    def generate_bill_number(cls, bill_type):
        """生成账单号"""
        prefix_map = {
            'storage': 'ST',
            'transport': 'TR',
            'service': 'SV',
            'penalty': 'PN'
        }
        prefix = prefix_map.get(bill_type, 'BL')
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 查找当天同类型账单数量
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        count = cls.query.filter(
            cls.bill_type == bill_type,
            cls.created_at >= today_start
        ).count()
        
        return f"{prefix}{timestamp}{count+1:03d}"


class Payment(BaseModel):
    """支付记录模型"""
    __tablename__ = 'payments'
    
    # 关联账单
    bill_id = db.Column(db.Integer, db.ForeignKey('bills.id'))
    bill = db.relationship('Bill', backref='payments')
    
    # 关联订单（直接支付订单的情况）
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'))
    order = db.relationship('Order', backref='payments')
    
    # 支付信息
    amount = db.Column(Numeric(12, 2), nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)  # cash, bank_transfer, alipay, wechat, credit_card
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 支付凭证
    reference = db.Column(db.String(100))  # 支付参考号/流水号
    receipt_image = db.Column(db.String(255))  # 支付凭证图片
    
    # 状态
    status = db.Column(db.String(20), default='completed')  # pending, completed, failed, refunded
    
    # 处理人
    processed_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    processed_by = db.relationship('User', backref='processed_payments')
    
    # 备注
    notes = db.Column(db.Text)
    
    def __repr__(self):
        return f'<Payment {self.amount} - {self.payment_method}>'
    
    def refund(self, refund_amount=None, reason=None):
        """退款"""
        if self.status != 'completed':
            raise ValueError("只能对已完成的支付进行退款")
        
        refund_amount = refund_amount or self.amount
        if refund_amount > self.amount:
            raise ValueError("退款金额不能超过支付金额")
        
        # 创建退款记录
        refund = Refund(
            payment_id=self.id,
            amount=refund_amount,
            reason=reason
        )
        refund.save()
        
        # 如果全额退款，更新支付状态
        if refund_amount == self.amount:
            self.status = 'refunded'
            db.session.commit()
        
        # 更新账单支付金额
        if self.bill:
            self.bill.paid_amount -= refund_amount
            if self.bill.paid_amount < self.bill.total_amount:
                self.bill.status = 'pending'
            db.session.commit()
        
        # 更新企业余额
        if self.bill:
            self.bill.company.update_balance(refund_amount, f"退款 {self.reference or ''}")
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 转换Decimal类型
        if data.get('amount'):
            data['amount'] = float(data['amount'])
        return data


class Refund(BaseModel):
    """退款记录模型"""
    __tablename__ = 'refunds'
    
    payment_id = db.Column(db.Integer, db.ForeignKey('payments.id'), nullable=False)
    payment = db.relationship('Payment', backref='refunds')
    
    amount = db.Column(Numeric(12, 2), nullable=False)
    reason = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, completed, failed
    
    refund_date = db.Column(db.DateTime, default=datetime.utcnow)
    processed_date = db.Column(db.DateTime)
    
    processed_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    processed_by = db.relationship('User', backref='processed_refunds')
    
    reference = db.Column(db.String(100))  # 退款参考号
    notes = db.Column(db.Text)
    
    def __repr__(self):
        return f'<Refund {self.amount}>'
    
    def complete(self, reference=None):
        """完成退款"""
        self.status = 'completed'
        self.processed_date = datetime.utcnow()
        self.reference = reference
        db.session.commit()
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        if data.get('amount'):
            data['amount'] = float(data['amount'])
        return data


class BalanceLog(BaseModel):
    """余额变动日志"""
    __tablename__ = 'balance_logs'
    
    company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=False)
    company = db.relationship('Company', backref='balance_logs')
    
    amount = db.Column(Numeric(12, 2), nullable=False)  # 变动金额（正数为增加，负数为减少）
    balance_before = db.Column(Numeric(15, 2))  # 变动前余额
    balance_after = db.Column(Numeric(15, 2))   # 变动后余额
    
    transaction_type = db.Column(db.String(20))  # payment, refund, adjustment, deposit
    reference_id = db.Column(db.String(50))      # 关联单据号
    
    description = db.Column(db.Text)
    
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', backref='balance_changes')
    
    def __repr__(self):
        return f'<BalanceLog {self.company.name} - {self.amount}>'
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 转换Decimal类型
        decimal_fields = ['amount', 'balance_before', 'balance_after']
        for field in decimal_fields:
            if data.get(field):
                data[field] = float(data[field])
        return data
