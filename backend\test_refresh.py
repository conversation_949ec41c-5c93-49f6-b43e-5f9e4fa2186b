#!/usr/bin/env python3
"""
测试刷新令牌端点
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from app import create_app, db
from app.models.user import User

def test_refresh_endpoint():
    app = create_app()
    
    with app.app_context():
        # 获取测试用户
        user = User.query.filter_by(username='admin').first()
        if not user:
            print("未找到admin用户")
            return
        
        # 生成令牌
        tokens = user.generate_tokens()
        refresh_token = tokens['refresh_token']
        
        print(f"生成的刷新令牌: {refresh_token[:50]}...")
        
        # 测试刷新端点
        url = 'http://localhost:5000/api/auth/refresh'
        headers = {
            'Authorization': f'Bearer {refresh_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.post(url, headers=headers, json={})
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                data = response.json()
                print("刷新成功!")
                print(f"新的访问令牌: {data.get('access_token', '')[:50]}...")
            else:
                print("刷新失败!")
                
        except requests.exceptions.ConnectionError:
            print("无法连接到服务器，请确保后端服务正在运行")
        except Exception as e:
            print(f"请求出错: {e}")

if __name__ == '__main__':
    test_refresh_endpoint()
