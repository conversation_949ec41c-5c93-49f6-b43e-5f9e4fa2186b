-- 初始化数据库脚本

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建管理员用户
INSERT INTO users (
    id, username, email, password_hash, real_name, role, is_active, is_verified, created_at, updated_at
) VALUES (
    1,
    'admin',
    '<EMAIL>',
    'pbkdf2:sha256:260000$salt$hash', -- 需要替换为实际的密码哈希
    '系统管理员',
    'admin',
    true,
    true,
    NOW(),
    NOW()
) ON CONFLICT (username) DO NOTHING;

-- 创建示例企业
INSERT INTO companies (
    id, name, code, type, contact_person, contact_phone, contact_email,
    address, business_license, status, entry_date, created_at, updated_at
) VALUES (
    1,
    '示例物流公司',
    'DEMO001',
    'logistics',
    '张经理',
    '***********',
    '<EMAIL>',
    '北京市朝阳区示例路123号',
    '91110000000000000X',
    'active',
    CURRENT_DATE,
    NOW(),
    NOW()
) ON CONFLICT (code) DO NOTHING;

-- 创建示例仓库
INSERT INTO warehouses (
    id, name, code, type, location, area, height, volume, max_capacity,
    current_capacity, status, company_id, rent_per_sqm, service_fee,
    created_at, updated_at
) VALUES (
    1,
    '1号仓库',
    'WH001',
    'normal',
    '北京市朝阳区物流园区A区',
    1000.0,
    8.0,
    8000.0,
    5000.0,
    0.0,
    'active',
    1,
    50.00,
    1000.00,
    NOW(),
    NOW()
) ON CONFLICT (code) DO NOTHING;

-- 创建示例货物
INSERT INTO goods (
    id, name, code, category, brand, unit, weight, volume, storage_type,
    unit_price, company_id, status, created_at, updated_at
) VALUES (
    1,
    '示例商品A',
    'GOODS001',
    '电子产品',
    '示例品牌',
    '件',
    1.5,
    0.01,
    'normal',
    100.00,
    1,
    'active',
    NOW(),
    NOW()
) ON CONFLICT (code) DO NOTHING;

-- 创建示例车辆
INSERT INTO vehicles (
    id, license_plate, vehicle_type, brand, model, load_capacity, volume_capacity,
    fuel_type, status, company_id, daily_rent, km_rate, created_at, updated_at
) VALUES (
    1,
    '京A12345',
    'truck',
    '东风',
    'DFL1160A',
    5.0,
    30.0,
    'diesel',
    'available',
    1,
    500.00,
    2.50,
    NOW(),
    NOW()
) ON CONFLICT (license_plate) DO NOTHING;

-- 创建示例司机
INSERT INTO drivers (
    id, name, phone, id_card, license_number, license_type, company_id,
    status, hire_date, base_salary, created_at, updated_at
) VALUES (
    1,
    '李师傅',
    '13900139000',
    '110101199001011234',
    'A1234567890',
    'A2',
    1,
    'available',
    CURRENT_DATE,
    8000.00,
    NOW(),
    NOW()
) ON CONFLICT (phone) DO NOTHING;

-- 创建索引以提高性能
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_company_id ON users(company_id);
CREATE INDEX IF NOT EXISTS idx_companies_code ON companies(code);
CREATE INDEX IF NOT EXISTS idx_companies_status ON companies(status);
CREATE INDEX IF NOT EXISTS idx_warehouses_code ON warehouses(code);
CREATE INDEX IF NOT EXISTS idx_warehouses_company_id ON warehouses(company_id);
CREATE INDEX IF NOT EXISTS idx_goods_code ON goods(code);
CREATE INDEX IF NOT EXISTS idx_goods_company_id ON goods(company_id);
CREATE INDEX IF NOT EXISTS idx_goods_category ON goods(category);
CREATE INDEX IF NOT EXISTS idx_vehicles_license_plate ON vehicles(license_plate);
CREATE INDEX IF NOT EXISTS idx_vehicles_company_id ON vehicles(company_id);
CREATE INDEX IF NOT EXISTS idx_drivers_phone ON drivers(phone);
CREATE INDEX IF NOT EXISTS idx_drivers_company_id ON drivers(company_id);
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_customer_company_id ON orders(customer_company_id);
CREATE INDEX IF NOT EXISTS idx_orders_service_company_id ON orders(service_company_id);
CREATE INDEX IF NOT EXISTS idx_inventory_goods_id ON inventory(goods_id);
CREATE INDEX IF NOT EXISTS idx_inventory_warehouse_id ON inventory(warehouse_id);
CREATE INDEX IF NOT EXISTS idx_bills_company_id ON bills(company_id);
CREATE INDEX IF NOT EXISTS idx_bills_status ON bills(status);
CREATE INDEX IF NOT EXISTS idx_deliveries_status ON deliveries(status);
CREATE INDEX IF NOT EXISTS idx_deliveries_driver_id ON deliveries(driver_id);

-- 设置序列的起始值
SELECT setval('users_id_seq', 1, true);
SELECT setval('companies_id_seq', 1, true);
SELECT setval('warehouses_id_seq', 1, true);
SELECT setval('goods_id_seq', 1, true);
SELECT setval('vehicles_id_seq', 1, true);
SELECT setval('drivers_id_seq', 1, true);
