from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from marshmallow import Schema, fields, validate, ValidationError
from app import db
from app.models.warehouse import Warehouse
from app.models.goods import Goods, Inventory
from app.models.user import User

warehouse_bp = Blueprint('warehouse', __name__)

# 权限检查装饰器
def require_permission(permission):
    def decorator(f):
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.get_by_id(current_user_id)
            if not user or not user.has_permission(permission):
                return jsonify({'error': '权限不足'}), 403
            return f(*args, **kwargs)
        decorated_function.__name__ = f.__name__
        return decorated_function
    return decorator

# 验证模式
class WarehouseSchema(Schema):
    name = fields.Str(required=True, validate=validate.Length(min=1, max=100))
    code = fields.Str(required=True, validate=validate.Length(min=1, max=20))
    type = fields.Str(required=True, validate=validate.OneOf(['normal', 'cold', 'dangerous']))
    location = fields.Str(validate=validate.Length(max=200))
    area = fields.Float(validate=validate.Range(min=0))
    height = fields.Float(validate=validate.Range(min=0))
    max_capacity = fields.Float(validate=validate.Range(min=0))
    company_id = fields.Int(required=True)
    manager_id = fields.Int()
    rent_per_sqm = fields.Decimal(places=2)
    service_fee = fields.Decimal(places=2)
    description = fields.Str()

class GoodsSchema(Schema):
    name = fields.Str(required=True, validate=validate.Length(min=1, max=100))
    code = fields.Str(required=True, validate=validate.Length(min=1, max=50))
    category = fields.Str(required=True, validate=validate.Length(min=1, max=50))
    brand = fields.Str(validate=validate.Length(max=50))
    model = fields.Str(validate=validate.Length(max=50))
    unit = fields.Str(required=True, validate=validate.Length(min=1, max=20))
    weight = fields.Float(validate=validate.Range(min=0))
    volume = fields.Float(validate=validate.Range(min=0))
    storage_type = fields.Str(validate=validate.OneOf(['normal', 'cold', 'dangerous']))
    unit_price = fields.Decimal(places=2)
    company_id = fields.Int(required=True)
    description = fields.Str()

class InventorySchema(Schema):
    goods_id = fields.Int(required=True)
    warehouse_id = fields.Int(required=True)
    quantity = fields.Float(required=True, validate=validate.Range(min=0))
    location = fields.Str(validate=validate.Length(max=50))
    batch_number = fields.Str(validate=validate.Length(max=50))
    production_date = fields.Date()
    expiry_date = fields.Date()

# 初始化验证器
warehouse_schema = WarehouseSchema()
goods_schema = GoodsSchema()
inventory_schema = InventorySchema()

# 仓库管理接口
@warehouse_bp.route('/warehouses', methods=['GET'])
@jwt_required()
@require_permission('warehouse')
def get_warehouses():
    """获取仓库列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    company_id = request.args.get('company_id', type=int)
    warehouse_type = request.args.get('type')
    status = request.args.get('status', 'active')
    
    # 构建查询条件
    filters = {'status': status}
    if company_id:
        filters['company_id'] = company_id
    if warehouse_type:
        filters['type'] = warehouse_type
    
    pagination = Warehouse.paginate(page=page, per_page=per_page, **filters)
    
    return jsonify({
        'warehouses': [warehouse.to_dict() for warehouse in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })

@warehouse_bp.route('/warehouses', methods=['POST'])
@jwt_required()
@require_permission('warehouse')
def create_warehouse():
    """创建仓库"""
    try:
        data = warehouse_schema.load(request.json)
    except ValidationError as err:
        return jsonify({'error': '输入数据无效', 'details': err.messages}), 400
    
    # 检查仓库代码是否已存在
    if Warehouse.get_by_code(data['code']):
        return jsonify({'error': '仓库代码已存在'}), 400
    
    warehouse = Warehouse(**data)
    
    try:
        warehouse.save()
        return jsonify({
            'message': '仓库创建成功',
            'warehouse': warehouse.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '创建失败，请稍后重试'}), 500

@warehouse_bp.route('/warehouses/<int:warehouse_id>', methods=['GET'])
@jwt_required()
@require_permission('warehouse')
def get_warehouse(warehouse_id):
    """获取仓库详情"""
    warehouse = Warehouse.get_by_id(warehouse_id)
    if not warehouse:
        return jsonify({'error': '仓库不存在'}), 404
    
    return jsonify(warehouse.to_dict())

@warehouse_bp.route('/warehouses/<int:warehouse_id>', methods=['PUT'])
@jwt_required()
@require_permission('warehouse')
def update_warehouse(warehouse_id):
    """更新仓库信息"""
    warehouse = Warehouse.get_by_id(warehouse_id)
    if not warehouse:
        return jsonify({'error': '仓库不存在'}), 404
    
    try:
        data = warehouse_schema.load(request.json, partial=True)
    except ValidationError as err:
        return jsonify({'error': '输入数据无效', 'details': err.messages}), 400
    
    # 检查仓库代码是否与其他仓库冲突
    if 'code' in data:
        existing = Warehouse.get_by_code(data['code'])
        if existing and existing.id != warehouse_id:
            return jsonify({'error': '仓库代码已存在'}), 400
    
    # 更新仓库信息
    for key, value in data.items():
        setattr(warehouse, key, value)
    
    try:
        db.session.commit()
        return jsonify({
            'message': '仓库更新成功',
            'warehouse': warehouse.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '更新失败，请稍后重试'}), 500

@warehouse_bp.route('/warehouses/<int:warehouse_id>', methods=['DELETE'])
@jwt_required()
@require_permission('warehouse')
def delete_warehouse(warehouse_id):
    """删除仓库"""
    warehouse = Warehouse.get_by_id(warehouse_id)
    if not warehouse:
        return jsonify({'error': '仓库不存在'}), 404
    
    # 检查仓库是否有库存
    if warehouse.inventory_records:
        active_inventory = [inv for inv in warehouse.inventory_records if not inv.is_deleted and inv.quantity > 0]
        if active_inventory:
            return jsonify({'error': '仓库中还有库存，无法删除'}), 400
    
    try:
        warehouse.delete()
        return jsonify({'message': '仓库删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '删除失败，请稍后重试'}), 500

# 货物管理接口
@warehouse_bp.route('/goods', methods=['GET'])
@jwt_required()
@require_permission('warehouse')
def get_goods():
    """获取货物列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    company_id = request.args.get('company_id', type=int)
    category = request.args.get('category')
    status = request.args.get('status', 'active')
    
    # 构建查询条件
    filters = {'status': status}
    if company_id:
        filters['company_id'] = company_id
    if category:
        filters['category'] = category
    
    pagination = Goods.paginate(page=page, per_page=per_page, **filters)
    
    return jsonify({
        'goods': [goods.to_dict() for goods in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })

@warehouse_bp.route('/goods', methods=['POST'])
@jwt_required()
@require_permission('warehouse')
def create_goods():
    """创建货物"""
    try:
        data = goods_schema.load(request.json)
    except ValidationError as err:
        return jsonify({'error': '输入数据无效', 'details': err.messages}), 400
    
    # 检查货物代码是否已存在
    if Goods.get_by_code(data['code']):
        return jsonify({'error': '货物代码已存在'}), 400
    
    goods = Goods(**data)
    
    try:
        goods.save()
        return jsonify({
            'message': '货物创建成功',
            'goods': goods.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '创建失败，请稍后重试'}), 500

# 库存管理接口
@warehouse_bp.route('/inventory', methods=['GET'])
@jwt_required()
@require_permission('warehouse')
def get_inventory():
    """获取库存列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    warehouse_id = request.args.get('warehouse_id', type=int)
    goods_id = request.args.get('goods_id', type=int)
    low_stock = request.args.get('low_stock', type=bool)
    
    query = Inventory.query.filter_by(is_deleted=False)
    
    if warehouse_id:
        query = query.filter_by(warehouse_id=warehouse_id)
    if goods_id:
        query = query.filter_by(goods_id=goods_id)
    if low_stock:
        query = query.filter(Inventory.quantity <= 10)
    
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'inventory': [inv.to_dict() for inv in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })

@warehouse_bp.route('/inventory/in', methods=['POST'])
@jwt_required()
@require_permission('warehouse')
def inventory_in():
    """入库操作"""
    try:
        data = inventory_schema.load(request.json)
    except ValidationError as err:
        return jsonify({'error': '输入数据无效', 'details': err.messages}), 400
    
    # 查找或创建库存记录
    inventory = Inventory.get_by_goods_and_warehouse(data['goods_id'], data['warehouse_id'])
    
    if inventory:
        # 更新现有库存
        inventory.update_quantity(data['quantity'], 'in')
    else:
        # 创建新库存记录
        inventory = Inventory(**data)
        inventory.entry_date = datetime.utcnow().date()
        inventory.save()
    
    # 更新仓库容量
    goods = Goods.get_by_id(data['goods_id'])
    if goods and goods.volume:
        warehouse = Warehouse.get_by_id(data['warehouse_id'])
        warehouse.update_capacity(goods.volume * data['quantity'])
    
    return jsonify({
        'message': '入库成功',
        'inventory': inventory.to_dict()
    })

@warehouse_bp.route('/inventory/out', methods=['POST'])
@jwt_required()
@require_permission('warehouse')
def inventory_out():
    """出库操作"""
    goods_id = request.json.get('goods_id')
    warehouse_id = request.json.get('warehouse_id')
    quantity = request.json.get('quantity')
    
    if not all([goods_id, warehouse_id, quantity]):
        return jsonify({'error': '缺少必要参数'}), 400
    
    if quantity <= 0:
        return jsonify({'error': '出库数量必须大于0'}), 400
    
    inventory = Inventory.get_by_goods_and_warehouse(goods_id, warehouse_id)
    if not inventory:
        return jsonify({'error': '库存记录不存在'}), 404
    
    if inventory.available_quantity < quantity:
        return jsonify({'error': '库存不足'}), 400
    
    try:
        inventory.update_quantity(-quantity, 'out')
        
        # 更新仓库容量
        goods = Goods.get_by_id(goods_id)
        if goods and goods.volume:
            warehouse = Warehouse.get_by_id(warehouse_id)
            warehouse.update_capacity(-goods.volume * quantity)
        
        return jsonify({
            'message': '出库成功',
            'inventory': inventory.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
