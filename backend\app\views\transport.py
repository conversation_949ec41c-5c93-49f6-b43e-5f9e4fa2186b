from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from marshmallow import Schema, fields, validate, ValidationError
from app import db
from app.models.vehicle import Vehicle
from app.models.driver import Driver
from app.models.order import Order
from app.models.user import User

transport_bp = Blueprint('transport', __name__)

# 权限检查装饰器
def require_permission(permission):
    def decorator(f):
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.get_by_id(current_user_id)
            if not user or not user.has_permission(permission):
                return jsonify({'error': '权限不足'}), 403
            return f(*args, **kwargs)
        decorated_function.__name__ = f.__name__
        return decorated_function
    return decorator

# 车辆管理接口
@transport_bp.route('/vehicles', methods=['GET'])
@jwt_required()
@require_permission('transport')
def get_vehicles():
    """获取车辆列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    company_id = request.args.get('company_id', type=int)
    vehicle_type = request.args.get('type')
    status = request.args.get('status')
    
    # 构建查询条件
    filters = {}
    if company_id:
        filters['company_id'] = company_id
    if vehicle_type:
        filters['vehicle_type'] = vehicle_type
    if status:
        filters['status'] = status
    
    pagination = Vehicle.paginate(page=page, per_page=per_page, **filters)
    
    return jsonify({
        'vehicles': [vehicle.to_dict() for vehicle in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })

@transport_bp.route('/vehicles/available', methods=['GET'])
@jwt_required()
@require_permission('transport')
def get_available_vehicles():
    """获取可用车辆"""
    company_id = request.args.get('company_id', type=int)
    vehicle_type = request.args.get('type')
    
    vehicles = Vehicle.get_available_vehicles(company_id, vehicle_type)
    
    return jsonify({
        'vehicles': [vehicle.to_dict() for vehicle in vehicles]
    })

# 司机管理接口
@transport_bp.route('/drivers', methods=['GET'])
@jwt_required()
@require_permission('transport')
def get_drivers():
    """获取司机列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    company_id = request.args.get('company_id', type=int)
    status = request.args.get('status')
    
    # 构建查询条件
    filters = {}
    if company_id:
        filters['company_id'] = company_id
    if status:
        filters['status'] = status
    
    pagination = Driver.paginate(page=page, per_page=per_page, **filters)
    
    return jsonify({
        'drivers': [driver.to_dict() for driver in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })

@transport_bp.route('/drivers/available', methods=['GET'])
@jwt_required()
@require_permission('transport')
def get_available_drivers():
    """获取可用司机"""
    company_id = request.args.get('company_id', type=int)
    license_type = request.args.get('license_type')
    
    drivers = Driver.get_available_drivers(company_id, license_type)
    
    return jsonify({
        'drivers': [driver.to_dict() for driver in drivers]
    })

# 运输订单接口
@transport_bp.route('/orders', methods=['GET'])
@jwt_required()
@require_permission('transport')
def get_transport_orders():
    """获取运输订单列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    status = request.args.get('status')
    company_id = request.args.get('company_id', type=int)
    
    query = Order.query.filter_by(order_type='transport', is_deleted=False)
    
    if status:
        query = query.filter_by(status=status)
    if company_id:
        query = query.filter(
            (Order.customer_company_id == company_id) | 
            (Order.service_company_id == company_id)
        )
    
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'orders': [order.to_dict() for order in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })

@transport_bp.route('/orders/<int:order_id>/assign', methods=['POST'])
@jwt_required()
@require_permission('transport')
def assign_transport_order():
    """分配运输订单"""
    order_id = request.json.get('order_id')
    vehicle_id = request.json.get('vehicle_id')
    driver_id = request.json.get('driver_id')
    
    if not all([order_id, vehicle_id, driver_id]):
        return jsonify({'error': '缺少必要参数'}), 400
    
    order = Order.get_by_id(order_id)
    if not order or order.order_type != 'transport':
        return jsonify({'error': '运输订单不存在'}), 404
    
    if order.status != 'confirmed':
        return jsonify({'error': '订单状态不允许分配'}), 400
    
    vehicle = Vehicle.get_by_id(vehicle_id)
    driver = Driver.get_by_id(driver_id)
    
    if not vehicle or not vehicle.is_available:
        return jsonify({'error': '车辆不可用'}), 400
    
    if not driver or not driver.can_drive:
        return jsonify({'error': '司机不可用'}), 400
    
    try:
        # 分配车辆和司机
        order.vehicle_id = vehicle_id
        order.driver_id = driver_id
        order.update_status('in_progress')
        
        # 更新车辆和司机状态
        vehicle.assign_driver(driver_id)
        driver.set_status('driving')
        
        return jsonify({
            'message': '订单分配成功',
            'order': order.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
