from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func, and_
from datetime import datetime, timedelta
from app import db
from app.models.user import User
from app.models.warehouse import Warehouse
from app.models.goods import Inventory
from app.models.vehicle import Vehicle
from app.models.driver import Driver
from app.models.order import Order
from app.models.delivery import Delivery
from app.models.finance import Bill

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/overview', methods=['GET'])
@jwt_required()
def get_overview():
    """获取仪表板概览数据"""
    current_user_id = get_jwt_identity()
    user = User.get_by_id(int(current_user_id))
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 根据用户角色和企业过滤数据
    company_filter = {}
    if user.role != 'admin' and user.company_id:
        company_filter['company_id'] = user.company_id
    
    # 基础统计
    stats = {
        'warehouses': {
            'total': Warehouse.query.filter_by(is_deleted=False, **company_filter).count(),
            'active': Warehouse.query.filter_by(status='active', is_deleted=False, **company_filter).count()
        },
        'vehicles': {
            'total': Vehicle.query.filter_by(is_deleted=False, **company_filter).count(),
            'available': Vehicle.query.filter_by(status='available', is_deleted=False, **company_filter).count(),
            'in_use': Vehicle.query.filter_by(status='in_use', is_deleted=False, **company_filter).count()
        },
        'drivers': {
            'total': Driver.query.filter_by(is_deleted=False, **company_filter).count(),
            'available': Driver.query.filter_by(status='available', is_deleted=False, **company_filter).count(),
            'driving': Driver.query.filter_by(status='driving', is_deleted=False, **company_filter).count()
        }
    }
    
    # 订单统计
    today = datetime.now().date()
    week_ago = today - timedelta(days=7)
    
    order_query = Order.query.filter_by(is_deleted=False)
    if user.role != 'admin' and user.company_id:
        order_query = order_query.filter(
            (Order.customer_company_id == user.company_id) | 
            (Order.service_company_id == user.company_id)
        )
    
    stats['orders'] = {
        'total': order_query.count(),
        'pending': order_query.filter_by(status='pending').count(),
        'in_progress': order_query.filter_by(status='in_progress').count(),
        'completed': order_query.filter_by(status='completed').count(),
        'this_week': order_query.filter(Order.created_at >= week_ago).count()
    }
    
    # 配送统计
    delivery_query = Delivery.query.filter_by(is_deleted=False)
    
    stats['deliveries'] = {
        'total': delivery_query.count(),
        'pending': delivery_query.filter_by(status='pending').count(),
        'in_transit': delivery_query.filter_by(status='in_transit').count(),
        'delivered': delivery_query.filter_by(status='delivered').count(),
        'today': delivery_query.filter(func.date(Delivery.created_at) == today).count()
    }
    
    # 财务统计
    bill_query = Bill.query.filter_by(is_deleted=False)
    if user.role != 'admin' and user.company_id:
        bill_query = bill_query.filter_by(company_id=user.company_id)
    
    total_amount = bill_query.with_entities(func.sum(Bill.total_amount)).scalar() or 0
    paid_amount = bill_query.with_entities(func.sum(Bill.paid_amount)).scalar() or 0
    
    stats['finance'] = {
        'total_bills': bill_query.count(),
        'pending_bills': bill_query.filter_by(status='pending').count(),
        'overdue_bills': bill_query.filter(and_(Bill.status == 'pending', Bill.due_date < today)).count(),
        'total_amount': float(total_amount),
        'paid_amount': float(paid_amount),
        'outstanding_amount': float(total_amount - paid_amount)
    }
    
    return jsonify({
        'stats': stats,
        'user_info': {
            'role': user.role,
            'company_id': user.company_id,
            'company_name': user.company.name if user.company else None
        }
    })

@dashboard_bp.route('/recent-activities', methods=['GET'])
@jwt_required()
def get_recent_activities():
    """获取最近活动"""
    limit = request.args.get('limit', 10, type=int)
    current_user_id = get_jwt_identity()
    user = User.get_by_id(int(current_user_id))
    
    activities = []
    
    # 最近订单
    order_query = Order.query.filter_by(is_deleted=False).order_by(Order.created_at.desc())
    if user.role != 'admin' and user.company_id:
        order_query = order_query.filter(
            (Order.customer_company_id == user.company_id) | 
            (Order.service_company_id == user.company_id)
        )
    
    recent_orders = order_query.limit(limit//2).all()
    for order in recent_orders:
        activities.append({
            'type': 'order',
            'title': f'新订单 {order.order_number}',
            'description': f'{order.order_type} 订单已创建',
            'time': order.created_at.isoformat(),
            'status': order.status
        })
    
    # 最近配送
    recent_deliveries = Delivery.query.filter_by(is_deleted=False)\
        .order_by(Delivery.created_at.desc()).limit(limit//2).all()
    
    for delivery in recent_deliveries:
        activities.append({
            'type': 'delivery',
            'title': f'配送任务 {delivery.delivery_number}',
            'description': f'配送状态: {delivery.status}',
            'time': delivery.created_at.isoformat(),
            'status': delivery.status
        })
    
    # 按时间排序
    activities.sort(key=lambda x: x['time'], reverse=True)
    
    return jsonify({
        'activities': activities[:limit]
    })

@dashboard_bp.route('/charts/orders', methods=['GET'])
@jwt_required()
def get_order_charts():
    """获取订单图表数据"""
    days = request.args.get('days', 7, type=int)
    current_user_id = get_jwt_identity()
    user = User.get_by_id(int(current_user_id))
    
    # 计算日期范围
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days-1)
    
    # 构建查询
    order_query = Order.query.filter(
        Order.created_at >= start_date,
        Order.is_deleted == False
    )
    
    if user.role != 'admin' and user.company_id:
        order_query = order_query.filter(
            (Order.customer_company_id == user.company_id) | 
            (Order.service_company_id == user.company_id)
        )
    
    # 按日期分组统计
    daily_orders = db.session.query(
        func.date(Order.created_at).label('date'),
        func.count(Order.id).label('count')
    ).filter(
        Order.created_at >= start_date,
        Order.is_deleted == False
    ).group_by(func.date(Order.created_at)).all()
    
    # 按状态统计
    status_stats = db.session.query(
        Order.status,
        func.count(Order.id).label('count')
    ).filter(
        Order.created_at >= start_date,
        Order.is_deleted == False
    ).group_by(Order.status).all()
    
    return jsonify({
        'daily_orders': [{'date': str(item.date), 'count': item.count} for item in daily_orders],
        'status_distribution': [{'status': item.status, 'count': item.count} for item in status_stats]
    })

@dashboard_bp.route('/alerts', methods=['GET'])
@jwt_required()
def get_alerts():
    """获取系统警报"""
    current_user_id = get_jwt_identity()
    user = User.get_by_id(int(current_user_id))
    
    alerts = []
    
    # 低库存警报
    try:
        low_stock_items = Inventory.get_low_stock(threshold=10)
        for item in low_stock_items:
            if user.role == 'admin' or (user.company_id and item.goods and item.goods.company_id == user.company_id):
                alerts.append({
                    'type': 'warning',
                    'title': '库存不足',
                    'message': f'{item.goods.name} 库存仅剩 {item.quantity} {item.goods.unit}',
                    'time': datetime.now().isoformat()
                })
    except Exception as e:
        print(f"低库存警报查询错误: {e}")
    
    # 逾期订单警报
    try:
        overdue_orders = Order.query.filter(
            Order.required_date < datetime.now(),
            Order.status.in_(['pending', 'confirmed', 'in_progress']),
            Order.is_deleted == False
        ).all()

        for order in overdue_orders:
            if user.role == 'admin' or (user.company_id and
                (order.customer_company_id == user.company_id or order.service_company_id == user.company_id)):
                alerts.append({
                    'type': 'error',
                    'title': '订单逾期',
                    'message': f'订单 {order.order_number} 已逾期',
                    'time': order.required_date.isoformat() if order.required_date else datetime.now().isoformat()
                })
    except Exception as e:
        print(f"逾期订单警报查询错误: {e}")
    
    # 车辆维护提醒
    try:
        vehicles_need_maintenance = Vehicle.query.filter_by(is_deleted=False).all()
        for vehicle in vehicles_need_maintenance:
            if vehicle.needs_maintenance and (user.role == 'admin' or
                (user.company_id and vehicle.company_id == user.company_id)):
                alerts.append({
                    'type': 'info',
                    'title': '车辆维护',
                    'message': f'车辆 {vehicle.license_plate} 需要维护',
                    'time': datetime.now().isoformat()
                })
    except Exception as e:
        print(f"车辆维护提醒查询错误: {e}")
    
    return jsonify({
        'alerts': alerts[:20]  # 限制返回数量
    })
