#!/usr/bin/env python3
"""
简单数据库初始化脚本 - 只创建基本数据
"""
import os
import sys
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db

# 导入基本模型
from app.models.user import User
from app.models.company import Company

def init_database():
    """初始化数据库"""
    print("开始初始化数据库...")
    
    app = create_app('development')
    
    with app.app_context():
        try:
            # 删除所有表（如果存在）
            print("删除现有表...")
            db.drop_all()
            
            # 创建所有表
            print("创建数据库表...")
            db.create_all()
            
            # 创建基本数据
            create_basic_data()
            
            print("✓ 数据库初始化完成！")
            return True
            
        except Exception as e:
            print(f"✗ 数据库初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def create_basic_data():
    """创建基本数据"""
    print("创建基本数据...")
    
    # 创建默认企业
    company = Company(
        name='示例物流公司',
        code='DEMO001',
        type='logistics',
        contact_person='张三',
        contact_phone='***********',
        contact_email='<EMAIL>',
        address='北京市朝阳区示例路123号',
        business_license='91110000000000000X',
        legal_person='张三',
        status='active',
        entry_date=date.today(),
        credit_limit=1000000.00,
        current_balance=0.00,
        description='示例物流企业'
    )
    company.save()
    print("✓ 创建默认企业")
    
    # 创建管理员用户
    admin_user = User(
        username='admin',
        email='<EMAIL>',
        real_name='系统管理员',
        phone='***********',
        role='admin',
        is_active=True,
        is_verified=True,
        company_id=company.id
    )
    admin_user.set_password('admin123')
    admin_user.save()
    print("✓ 创建管理员用户 (用户名: admin, 密码: admin123)")
    
    # 创建普通用户
    user = User(
        username='user',
        email='<EMAIL>',
        real_name='普通用户',
        phone='***********',
        role='user',
        is_active=True,
        is_verified=True,
        company_id=company.id
    )
    user.set_password('user123')
    user.save()
    print("✓ 创建普通用户 (用户名: user, 密码: user123)")
    
    print("✓ 基本数据创建完成")

if __name__ == '__main__':
    success = init_database()
    if success:
        print("\n数据库初始化成功！")
        print("您可以使用以下账户登录系统：")
        print("管理员 - 用户名: admin, 密码: admin123")
        print("普通用户 - 用户名: user, 密码: user123")
        print("\n现在可以启动后端服务器了：")
        print("python run.py")
    else:
        print("\n数据库初始化失败！")
        sys.exit(1)
