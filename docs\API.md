# 物流园区信息系统 API 文档

## 概述

本文档描述了物流园区信息系统的RESTful API接口。所有API都使用JSON格式进行数据交换。

## 基础信息

- **Base URL**: `http://localhost:5000/api`
- **认证方式**: JWT Bearer Token
- **内容类型**: `application/json`

## 认证接口

### 用户登录
```http
POST /auth/login
```

**请求体**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**响应**:
```json
{
  "message": "登录成功",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin"
  },
  "tokens": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

### 用户注册
```http
POST /auth/register
```

### 刷新令牌
```http
POST /auth/refresh
```

### 用户登出
```http
POST /auth/logout
```

## 仓储管理接口

### 获取仓库列表
```http
GET /warehouse/warehouses
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 20)
- `company_id`: 企业ID
- `type`: 仓库类型 (normal/cold/dangerous)
- `status`: 状态 (active/maintenance/inactive)

**响应**:
```json
{
  "warehouses": [
    {
      "id": 1,
      "name": "1号仓库",
      "code": "WH001",
      "type": "normal",
      "location": "北京市朝阳区",
      "area": 1000.0,
      "utilization_rate": 65.5,
      "status": "active"
    }
  ],
  "pagination": {
    "page": 1,
    "pages": 5,
    "per_page": 20,
    "total": 100
  }
}
```

### 创建仓库
```http
POST /warehouse/warehouses
```

**请求体**:
```json
{
  "name": "新仓库",
  "code": "WH002",
  "type": "normal",
  "location": "上海市浦东新区",
  "area": 800.0,
  "height": 6.0,
  "max_capacity": 4000.0,
  "company_id": 1
}
```

### 更新仓库
```http
PUT /warehouse/warehouses/{id}
```

### 删除仓库
```http
DELETE /warehouse/warehouses/{id}
```

### 库存入库
```http
POST /warehouse/inventory/in
```

**请求体**:
```json
{
  "goods_id": 1,
  "warehouse_id": 1,
  "quantity": 100,
  "location": "A-01-01",
  "batch_number": "BATCH001"
}
```

### 库存出库
```http
POST /warehouse/inventory/out
```

## 运输管理接口

### 获取车辆列表
```http
GET /transport/vehicles
```

### 获取可用车辆
```http
GET /transport/vehicles/available
```

### 获取司机列表
```http
GET /transport/drivers
```

### 分配运输订单
```http
POST /transport/orders/{id}/assign
```

## 配送管理接口

### 获取配送列表
```http
GET /delivery/deliveries
```

### 获取活跃配送
```http
GET /delivery/deliveries/active
```

## 财务管理接口

### 获取账单列表
```http
GET /finance/bills
```

### 获取支付记录
```http
GET /finance/payments
```

## 仪表板接口

### 获取概览数据
```http
GET /dashboard/overview
```

**响应**:
```json
{
  "stats": {
    "warehouses": {
      "total": 10,
      "active": 8
    },
    "vehicles": {
      "total": 25,
      "available": 15,
      "in_use": 8
    },
    "orders": {
      "total": 150,
      "pending": 5,
      "in_progress": 12,
      "completed": 133
    },
    "finance": {
      "total_amount": 1500000.00,
      "paid_amount": 1200000.00,
      "outstanding_amount": 300000.00
    }
  }
}
```

### 获取最近活动
```http
GET /dashboard/recent-activities
```

### 获取订单图表数据
```http
GET /dashboard/charts/orders?days=7
```

### 获取系统警报
```http
GET /dashboard/alerts
```

## 错误处理

### 错误响应格式
```json
{
  "error": "错误描述",
  "details": {
    "field": ["具体错误信息"]
  }
}
```

### 常见错误码
- `400`: 请求参数错误
- `401`: 未授权或令牌过期
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 数据验证失败
- `500`: 服务器内部错误

## 认证说明

大部分API需要在请求头中包含JWT令牌：

```http
Authorization: Bearer <access_token>
```

令牌过期时间为24小时，可使用refresh_token刷新。

## 分页说明

列表接口支持分页，响应中包含分页信息：

```json
{
  "pagination": {
    "page": 1,        // 当前页码
    "pages": 10,      // 总页数
    "per_page": 20,   // 每页数量
    "total": 200,     // 总记录数
    "has_next": true, // 是否有下一页
    "has_prev": false // 是否有上一页
  }
}
```

## 数据格式说明

### 日期时间格式
- 日期: `YYYY-MM-DD`
- 日期时间: `YYYY-MM-DDTHH:mm:ss`

### 数值格式
- 金额: 保留2位小数
- 重量: 单位为千克(kg)
- 体积: 单位为立方米(m³)
- 面积: 单位为平方米(m²)

## 状态码说明

### 订单状态
- `pending`: 待处理
- `confirmed`: 已确认
- `in_progress`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

### 配送状态
- `pending`: 待分配
- `assigned`: 已分配
- `picked_up`: 已取货
- `in_transit`: 运输中
- `delivered`: 已送达
- `failed`: 配送失败

### 车辆状态
- `available`: 可用
- `in_use`: 使用中
- `maintenance`: 维护中
- `inactive`: 停用

## 示例代码

### JavaScript (Axios)
```javascript
// 登录
const login = async (username, password) => {
  const response = await axios.post('/api/auth/login', {
    username,
    password
  });
  
  // 保存令牌
  localStorage.setItem('access_token', response.data.tokens.access_token);
  
  return response.data;
};

// 获取仓库列表
const getWarehouses = async (page = 1) => {
  const response = await axios.get('/api/warehouse/warehouses', {
    params: { page },
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    }
  });
  
  return response.data;
};
```

### Python (Requests)
```python
import requests

# 登录
def login(username, password):
    response = requests.post('http://localhost:5000/api/auth/login', json={
        'username': username,
        'password': password
    })
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"登录失败: {response.json()}")

# 获取仓库列表
def get_warehouses(token, page=1):
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(
        'http://localhost:5000/api/warehouse/warehouses',
        params={'page': page},
        headers=headers
    )
    
    return response.json()
```

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现基础的CRUD接口
- 添加JWT认证
- 支持分页和筛选

### 联系方式

如有API相关问题，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.logistics.com/api
