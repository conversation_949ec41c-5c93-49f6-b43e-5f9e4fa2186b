from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.delivery import Delivery
from app.models.user import User

delivery_bp = Blueprint('delivery', __name__)

# 权限检查装饰器
def require_permission(permission):
    def decorator(f):
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.get_by_id(current_user_id)
            if not user or not user.has_permission(permission):
                return jsonify({'error': '权限不足'}), 403
            return f(*args, **kwargs)
        decorated_function.__name__ = f.__name__
        return decorated_function
    return decorator

@delivery_bp.route('/deliveries', methods=['GET'])
@jwt_required()
@require_permission('delivery')
def get_deliveries():
    """获取配送列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    status = request.args.get('status')
    driver_id = request.args.get('driver_id', type=int)
    
    query = Delivery.query.filter_by(is_deleted=False)
    
    if status:
        query = query.filter_by(status=status)
    if driver_id:
        query = query.filter_by(driver_id=driver_id)
    
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'deliveries': [delivery.to_dict() for delivery in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })

@delivery_bp.route('/deliveries/active', methods=['GET'])
@jwt_required()
@require_permission('delivery')
def get_active_deliveries():
    """获取活跃配送任务"""
    driver_id = request.args.get('driver_id', type=int)
    vehicle_id = request.args.get('vehicle_id', type=int)
    
    deliveries = Delivery.get_active_deliveries(driver_id, vehicle_id)
    
    return jsonify({
        'deliveries': [delivery.to_dict() for delivery in deliveries]
    })
