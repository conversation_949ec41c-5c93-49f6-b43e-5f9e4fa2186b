from app import db
from .base import BaseModel
from datetime import datetime
from sqlalchemy import Numeric

class Delivery(BaseModel):
    """配送模型"""
    __tablename__ = 'deliveries'
    
    # 基本信息
    delivery_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    status = db.Column(db.String(20), default='pending')  # pending, assigned, picked_up, in_transit, delivered, failed
    
    # 关联订单
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)
    order = db.relationship('Order', backref='deliveries')
    
    # 配送信息
    vehicle_id = db.Column(db.Integer, db.<PERSON>ey('vehicles.id'))
    vehicle = db.relationship('Vehicle', backref='deliveries')
    
    driver_id = db.Column(db.Integer, db.ForeignKey('drivers.id'))
    driver = db.relationship('Driver', backref='deliveries')
    
    # 时间信息
    scheduled_pickup_time = db.Column(db.DateTime)
    actual_pickup_time = db.Column(db.DateTime)
    scheduled_delivery_time = db.Column(db.DateTime)
    actual_delivery_time = db.Column(db.DateTime)
    
    # 地址信息
    pickup_address = db.Column(db.Text, nullable=False)
    pickup_contact = db.Column(db.String(50))
    pickup_phone = db.Column(db.String(20))
    pickup_latitude = db.Column(db.Float)
    pickup_longitude = db.Column(db.Float)
    
    delivery_address = db.Column(db.Text, nullable=False)
    delivery_contact = db.Column(db.String(50))
    delivery_phone = db.Column(db.String(20))
    delivery_latitude = db.Column(db.Float)
    delivery_longitude = db.Column(db.Float)
    
    # 路线信息
    planned_route = db.Column(db.Text)  # JSON格式存储路线点
    actual_route = db.Column(db.Text)   # 实际行驶路线
    distance = db.Column(db.Float)      # 距离(公里)
    estimated_duration = db.Column(db.Integer)  # 预计时长(分钟)
    actual_duration = db.Column(db.Integer)     # 实际时长(分钟)
    
    # 货物信息
    total_weight = db.Column(db.Float)  # 总重量
    total_volume = db.Column(db.Float)  # 总体积
    package_count = db.Column(db.Integer)  # 包裹数量
    
    # 费用信息
    delivery_fee = db.Column(Numeric(10, 2))
    fuel_cost = db.Column(Numeric(8, 2))
    toll_fee = db.Column(Numeric(8, 2))
    other_costs = db.Column(Numeric(8, 2))
    
    # 签收信息
    recipient_name = db.Column(db.String(50))
    recipient_phone = db.Column(db.String(20))
    signature_image = db.Column(db.String(255))  # 签名图片路径
    delivery_proof = db.Column(db.String(255))   # 配送凭证图片
    
    # 特殊要求
    special_instructions = db.Column(db.Text)
    temperature_requirement = db.Column(db.String(50))
    handling_requirements = db.Column(db.Text)
    
    # 优先级
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    
    # 评价信息
    customer_rating = db.Column(db.Integer)  # 1-5星评价
    customer_feedback = db.Column(db.Text)
    
    # 备注
    notes = db.Column(db.Text)
    
    def __repr__(self):
        return f'<Delivery {self.delivery_number}>'
    
    @property
    def is_overdue(self):
        """是否逾期"""
        if self.scheduled_delivery_time and self.status not in ['delivered', 'failed']:
            return datetime.utcnow() > self.scheduled_delivery_time
        return False
    
    @property
    def delivery_efficiency(self):
        """配送效率（实际时长/预计时长）"""
        if self.actual_duration and self.estimated_duration:
            return self.actual_duration / self.estimated_duration
        return None
    
    @property
    def current_location(self):
        """当前位置（如果在运输中）"""
        if self.status == 'in_transit' and self.vehicle:
            return self.vehicle.location
        return None
    
    def assign_vehicle_and_driver(self, vehicle_id, driver_id):
        """分配车辆和司机"""
        if self.status != 'pending':
            raise ValueError("只能为待分配的配送任务分配车辆和司机")
        
        from .vehicle import Vehicle
        from .driver import Driver
        
        vehicle = Vehicle.get_by_id(vehicle_id)
        driver = Driver.get_by_id(driver_id)
        
        if not vehicle or not vehicle.is_available:
            raise ValueError("车辆不可用")
        
        if not driver or not driver.can_drive:
            raise ValueError("司机不可用")
        
        self.vehicle_id = vehicle_id
        self.driver_id = driver_id
        self.status = 'assigned'
        
        # 更新车辆和司机状态
        vehicle.assign_driver(driver_id)
        driver.set_status('driving')
        
        db.session.commit()
    
    def start_pickup(self):
        """开始取货"""
        if self.status != 'assigned':
            raise ValueError("配送任务未分配，无法开始取货")
        
        self.status = 'picked_up'
        self.actual_pickup_time = datetime.utcnow()
        db.session.commit()
    
    def start_delivery(self):
        """开始配送"""
        if self.status != 'picked_up':
            raise ValueError("未完成取货，无法开始配送")
        
        self.status = 'in_transit'
        db.session.commit()
    
    def complete_delivery(self, recipient_name, recipient_phone, signature_image=None, delivery_proof=None):
        """完成配送"""
        if self.status != 'in_transit':
            raise ValueError("配送任务未在运输中，无法完成配送")
        
        self.status = 'delivered'
        self.actual_delivery_time = datetime.utcnow()
        self.recipient_name = recipient_name
        self.recipient_phone = recipient_phone
        self.signature_image = signature_image
        self.delivery_proof = delivery_proof
        
        # 计算实际时长
        if self.actual_pickup_time:
            duration = (self.actual_delivery_time - self.actual_pickup_time).total_seconds() / 60
            self.actual_duration = int(duration)
        
        # 释放车辆和司机
        if self.vehicle:
            self.vehicle.release_driver()
        if self.driver:
            self.driver.set_status('available')
        
        db.session.commit()
    
    def fail_delivery(self, reason):
        """配送失败"""
        self.status = 'failed'
        self.notes = (self.notes or '') + f"\n[{datetime.utcnow()}] 配送失败: {reason}"
        
        # 释放车辆和司机
        if self.vehicle:
            self.vehicle.release_driver()
        if self.driver:
            self.driver.set_status('available')
        
        db.session.commit()
    
    def update_location(self, latitude, longitude):
        """更新位置（用于实时跟踪）"""
        if self.vehicle:
            self.vehicle.location = f"{latitude},{longitude}"
            db.session.commit()
    
    def add_rating(self, rating, feedback=None):
        """添加客户评价"""
        if rating < 1 or rating > 5:
            raise ValueError("评价必须在1-5星之间")
        
        self.customer_rating = rating
        self.customer_feedback = feedback
        db.session.commit()
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 添加计算属性
        data['is_overdue'] = self.is_overdue
        data['delivery_efficiency'] = self.delivery_efficiency
        data['current_location'] = self.current_location
        
        # 转换Decimal类型
        decimal_fields = ['delivery_fee', 'fuel_cost', 'toll_fee', 'other_costs']
        for field in decimal_fields:
            if data.get(field):
                data[field] = float(data[field])
        
        return data
    
    @classmethod
    def generate_delivery_number(cls):
        """生成配送单号"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 查找当天配送单数量
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        count = cls.query.filter(cls.created_at >= today_start).count()
        
        return f"DL{timestamp}{count+1:04d}"
    
    @classmethod
    def get_active_deliveries(cls, driver_id=None, vehicle_id=None):
        """获取活跃配送任务"""
        query = cls.query.filter(
            cls.status.in_(['assigned', 'picked_up', 'in_transit']),
            cls.is_deleted == False
        )
        
        if driver_id:
            query = query.filter_by(driver_id=driver_id)
        if vehicle_id:
            query = query.filter_by(vehicle_id=vehicle_id)
        
        return query.all()
