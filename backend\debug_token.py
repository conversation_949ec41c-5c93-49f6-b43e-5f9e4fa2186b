#!/usr/bin/env python3
"""
调试JWT令牌的脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from flask_jwt_extended import decode_token
import jwt
from datetime import datetime

def debug_tokens():
    app = create_app()
    
    with app.app_context():
        print("=== JWT配置信息 ===")
        print(f"JWT_SECRET_KEY: {app.config.get('JWT_SECRET_KEY')}")
        print(f"JWT_ACCESS_TOKEN_EXPIRES: {app.config.get('JWT_ACCESS_TOKEN_EXPIRES')}")
        print(f"JWT_REFRESH_TOKEN_EXPIRES: {app.config.get('JWT_REFRESH_TOKEN_EXPIRES')}")
        print()
        
        # 检查用户状态
        print("=== 用户状态检查 ===")
        users = User.query.all()
        for user in users:
            print(f"用户: {user.username}, ID: {user.id}, 活跃: {user.is_active}, 角色: {user.role}")
        print()
        
        # 生成测试令牌
        print("=== 生成测试令牌 ===")
        if users:
            test_user = users[0]  # 使用第一个用户
            tokens = test_user.generate_tokens()
            print(f"为用户 {test_user.username} 生成的令牌:")
            print(f"Access Token: {tokens['access_token'][:50]}...")
            print(f"Refresh Token: {tokens['refresh_token'][:50]}...")
            
            # 解码令牌查看内容
            try:
                access_payload = jwt.decode(
                    tokens['access_token'], 
                    app.config['JWT_SECRET_KEY'], 
                    algorithms=['HS256']
                )
                print(f"Access Token 内容: {access_payload}")
                
                refresh_payload = jwt.decode(
                    tokens['refresh_token'], 
                    app.config['JWT_SECRET_KEY'], 
                    algorithms=['HS256']
                )
                print(f"Refresh Token 内容: {refresh_payload}")
                
                # 检查过期时间
                access_exp = datetime.fromtimestamp(access_payload['exp'])
                refresh_exp = datetime.fromtimestamp(refresh_payload['exp'])
                now = datetime.now()
                
                print(f"当前时间: {now}")
                print(f"Access Token 过期时间: {access_exp}")
                print(f"Refresh Token 过期时间: {refresh_exp}")
                print(f"Access Token 是否过期: {now > access_exp}")
                print(f"Refresh Token 是否过期: {now > refresh_exp}")
                
            except Exception as e:
                print(f"解码令牌时出错: {e}")

if __name__ == '__main__':
    debug_tokens()
