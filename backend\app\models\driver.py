from app import db
from .base import BaseModel
from sqlalchemy import Numeric

class Driver(BaseModel):
    """司机模型"""
    __tablename__ = 'drivers'
    
    # 基本信息
    name = db.Column(db.String(50), nullable=False, index=True)
    phone = db.Column(db.String(20), unique=True, nullable=False, index=True)
    id_card = db.Column(db.String(20), unique=True, nullable=False)
    gender = db.Column(db.String(10))
    birth_date = db.Column(db.Date)
    address = db.Column(db.Text)
    
    # 驾驶证信息
    license_number = db.Column(db.String(20), unique=True, nullable=False)
    license_type = db.Column(db.String(10), nullable=False)  # A1, A2, B1, B2, C1等
    license_issue_date = db.Column(db.Date)
    license_expiry_date = db.Column(db.Date)
    
    # 从业资格证
    qualification_number = db.Column(db.String(30))
    qualification_expiry = db.Column(db.Date)
    
    # 工作信息
    company_id = db.Column(db.Integer, db.ForeignKey('companies.id'), nullable=False)
    company = db.relationship('Company', backref='drivers')
    
    employee_number = db.Column(db.String(20))  # 员工编号
    hire_date = db.Column(db.Date)              # 入职日期
    salary_type = db.Column(db.String(20), default='monthly')  # monthly, daily, per_trip
    base_salary = db.Column(Numeric(8, 2))   # 基本工资
    
    # 状态信息
    status = db.Column(db.String(20), default='available')  # available, driving, rest, leave
    current_location = db.Column(db.String(200))
    
    # 驾驶记录
    total_mileage = db.Column(db.Float, default=0)  # 总里程
    total_trips = db.Column(db.Integer, default=0)  # 总趟数
    safety_score = db.Column(db.Float, default=100)  # 安全评分
    
    # 联系人信息
    emergency_contact = db.Column(db.String(50))    # 紧急联系人
    emergency_phone = db.Column(db.String(20))      # 紧急联系电话
    
    # 健康信息
    health_certificate = db.Column(db.String(50))   # 健康证号
    health_expiry = db.Column(db.Date)              # 健康证到期日
    
    # 备注
    description = db.Column(db.Text)
    avatar = db.Column(db.String(255))  # 头像
    
    def __repr__(self):
        return f'<Driver {self.name}>'
    
    @property
    def age(self):
        """年龄"""
        if self.birth_date:
            from datetime import date
            today = date.today()
            return today.year - self.birth_date.year - (
                (today.month, today.day) < (self.birth_date.month, self.birth_date.day)
            )
        return None
    
    @property
    def is_available(self):
        """是否可用"""
        return self.status == 'available' and not self.is_deleted
    
    @property
    def license_expired(self):
        """驾驶证是否过期"""
        if self.license_expiry_date:
            from datetime import date
            return date.today() > self.license_expiry_date
        return False
    
    @property
    def qualification_expired(self):
        """从业资格证是否过期"""
        if self.qualification_expiry:
            from datetime import date
            return date.today() > self.qualification_expiry
        return False
    
    @property
    def health_expired(self):
        """健康证是否过期"""
        if self.health_expiry:
            from datetime import date
            return date.today() > self.health_expiry
        return False
    
    @property
    def can_drive(self):
        """是否可以驾驶"""
        return (self.is_available and 
                not self.license_expired and 
                not self.qualification_expired and 
                not self.health_expired)
    
    def update_driving_record(self, mileage, trip_count=1):
        """更新驾驶记录"""
        self.total_mileage += mileage
        self.total_trips += trip_count
        db.session.commit()
    
    def update_safety_score(self, score_change):
        """更新安全评分"""
        new_score = self.safety_score + score_change
        self.safety_score = max(0, min(100, new_score))  # 限制在0-100之间
        db.session.commit()
    
    def set_status(self, status, location=None):
        """设置状态"""
        valid_statuses = ['available', 'driving', 'rest', 'leave']
        if status not in valid_statuses:
            raise ValueError(f"无效状态: {status}")
        
        self.status = status
        if location:
            self.current_location = location
        db.session.commit()
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        # 添加计算属性
        data['age'] = self.age
        data['is_available'] = self.is_available
        data['license_expired'] = self.license_expired
        data['qualification_expired'] = self.qualification_expired
        data['health_expired'] = self.health_expired
        data['can_drive'] = self.can_drive
        
        # 转换Decimal类型
        if data.get('base_salary'):
            data['base_salary'] = float(data['base_salary'])
        
        return data
    
    @classmethod
    def get_available_drivers(cls, company_id=None, license_type=None):
        """获取可用司机"""
        query = cls.query.filter_by(status='available', is_deleted=False)
        if company_id:
            query = query.filter_by(company_id=company_id)
        if license_type:
            query = query.filter_by(license_type=license_type)
        return query.all()
    
    @classmethod
    def get_by_phone(cls, phone):
        """根据手机号获取司机"""
        return cls.query.filter_by(phone=phone, is_deleted=False).first()
    
    @classmethod
    def get_by_license(cls, license_number):
        """根据驾驶证号获取司机"""
        return cls.query.filter_by(license_number=license_number, is_deleted=False).first()
